package library.management.system.database;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class DatabaseFixer {
    
    public static void main(String[] args) {
        try {
            System.out.println("Starting database fix for Condition column...");
            Connection conn = DatabaseConnection.getConnection();
            
            // Fix the Condition column values
            fixConditionColumnValues(conn);
            
            DatabaseConnection.closeConnection(conn);
            System.out.println("Database fix completed successfully!");
        } catch (Exception e) {
            System.err.println("Error fixing database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void fixConditionColumnValues(Connection conn) throws SQLException {
        try {
            // First check if there are any boolean values in the Condition column
            String checkSql = "SELECT CopyID, Condition FROM BookCopies";
            PreparedStatement checkStmt = conn.prepareStatement(checkSql);
            ResultSet rs = checkStmt.executeQuery();
            
            boolean needsFix = false;
            while (rs.next()) {
                Object condition = rs.getObject("Condition");
                if (condition != null && condition instanceof Boolean) {
                    needsFix = true;
                    break;
                }
            }
            rs.close();
            checkStmt.close();
            
            if (!needsFix) {
                System.out.println("No boolean values found in Condition column. No fix needed.");
                return;
            }
            
            // Update all 'true' values to 'Good'
            String updateGoodSql = "UPDATE BookCopies SET Condition = 'Good' WHERE Condition = 'true'";
            Statement updateGoodStmt = conn.createStatement();
            int goodRowsUpdated = updateGoodStmt.executeUpdate(updateGoodSql);
            updateGoodStmt.close();
            
            // Update all 'false' values to 'Poor'
            String updatePoorSql = "UPDATE BookCopies SET Condition = 'Poor' WHERE Condition = 'false'";
            Statement updatePoorStmt = conn.createStatement();
            int poorRowsUpdated = updatePoorStmt.executeUpdate(updatePoorSql);
            updatePoorStmt.close();
            
            System.out.println("Updated " + goodRowsUpdated + " rows from 'true' to 'Good'");
            System.out.println("Updated " + poorRowsUpdated + " rows from 'false' to 'Poor'");
            
        } catch (SQLException e) {
            System.err.println("Error fixing Condition column values: " + e.getMessage());
            throw e;
        }
    }
}
