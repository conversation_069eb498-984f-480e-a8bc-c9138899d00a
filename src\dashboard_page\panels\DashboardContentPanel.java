package dashboard_page.panels;

import javax.swing.*;
import javax.swing.border.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DecimalFormat;

import library.management.system.database.DatabaseConnection;

public class DashboardContentPanel extends JPanel {

    // Labels to display statistics
    private JLabel totalBooksValue;
    private JLabel booksBorrowedValue;
    private JLabel totalUsersValue;
    private JLabel overdueBooksValue;

    public DashboardContentPanel() {
        initComponents();
        loadDashboardData();
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));

        setBackground(new Color(240, 240, 240));

        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(240, 240, 240));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 50, 10, 50));

        JLabel titleLabel = new JLabel("Library Dashboard");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 28));
        titleLabel.setForeground(new Color(70, 70, 70));

        JLabel subtitleLabel = new JLabel("Overview of library statistics and key metrics");
        subtitleLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        subtitleLabel.setForeground(new Color(120, 120, 120));

        JPanel titlePanel = new JPanel();
        titlePanel.setLayout(new BoxLayout(titlePanel, BoxLayout.Y_AXIS));
        titlePanel.setBackground(new Color(240, 240, 240));
        titlePanel.add(titleLabel);
        titlePanel.add(Box.createRigidArea(new Dimension(0, 5)));
        titlePanel.add(subtitleLabel);

        headerPanel.add(titlePanel, BorderLayout.WEST);

        JPanel contentPanel = new JPanel(new GridLayout(2, 2, 30, 30));
        contentPanel.setBorder(BorderFactory.createEmptyBorder(20, 50, 50, 50));
        contentPanel.setBackground(new Color(240, 240, 240));

        // Create value labels first
        totalBooksValue = new JLabel("0");
        totalBooksValue.setFont(new Font("Arial", Font.BOLD, 48));
        totalBooksValue.setForeground(Color.WHITE);
        totalBooksValue.setAlignmentX(Component.CENTER_ALIGNMENT);

        booksBorrowedValue = new JLabel("0");
        booksBorrowedValue.setFont(new Font("Arial", Font.BOLD, 48));
        booksBorrowedValue.setForeground(Color.WHITE);
        booksBorrowedValue.setAlignmentX(Component.CENTER_ALIGNMENT);

        totalUsersValue = new JLabel("0");
        totalUsersValue.setFont(new Font("Arial", Font.BOLD, 48));
        totalUsersValue.setForeground(Color.WHITE);
        totalUsersValue.setAlignmentX(Component.CENTER_ALIGNMENT);

        overdueBooksValue = new JLabel("0");
        overdueBooksValue.setFont(new Font("Arial", Font.BOLD, 48));
        overdueBooksValue.setForeground(Color.WHITE);
        overdueBooksValue.setAlignmentX(Component.CENTER_ALIGNMENT);

        // Create panels with the value labels
        JPanel totalBooksPanel = createStatPanel("Total Books", totalBooksValue, new Color(100, 181, 246));
        JPanel booksBorrowedPanel = createStatPanel("Books Borrowed", booksBorrowedValue, new Color(255, 183, 77));
        JPanel totalUsersPanel = createStatPanel("Total Users", totalUsersValue, new Color(174, 213, 129));
        JPanel overdueBooksPanel = createStatPanel("Overdue Books", overdueBooksValue, new Color(239, 154, 154));

        contentPanel.add(totalBooksPanel);
        contentPanel.add(booksBorrowedPanel);
        contentPanel.add(totalUsersPanel);
        contentPanel.add(overdueBooksPanel);

        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(240, 240, 240));
        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(contentPanel, BorderLayout.CENTER);

        add(mainPanel, BorderLayout.CENTER);
    }

    /**
     * Load real data from the database for the dashboard
     * Public method to allow refreshing from outside
     */
    public void loadDashboardData() {
        try {
            Connection conn = DatabaseConnection.getConnection();

            // Get total books count
            int totalBooks = 0;
            String booksSql = "SELECT COUNT(*) FROM Books";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(booksSql)) {
                if (rs.next()) {
                    totalBooks = rs.getInt(1);
                }
            }

            // Get total book copies count (for borrowed books)
            int totalBorrowed = 0;
            String borrowedSql = "SELECT COUNT(*) FROM BookCopies WHERE Status = 'Borrowed'";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(borrowedSql)) {
                if (rs.next()) {
                    totalBorrowed = rs.getInt(1);
                }
            }

            // Get total users count
            int totalUsers = 0;
            String usersSql = "SELECT COUNT(*) FROM Users";
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(usersSql)) {
                if (rs.next()) {
                    totalUsers = rs.getInt(1);
                }
            }

            // For overdue books, we would need a more complex query
            // For now, just use a placeholder or random number
            int overdueBooks = 0;

            // Format numbers with commas for thousands
            DecimalFormat formatter = new DecimalFormat("#,###");

            // Update the UI with the fetched data
            totalBooksValue.setText(formatter.format(totalBooks));
            booksBorrowedValue.setText(formatter.format(totalBorrowed));
            totalUsersValue.setText(formatter.format(totalUsers));
            overdueBooksValue.setText(formatter.format(overdueBooks));

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            System.err.println("Error loading dashboard data: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private JPanel createStatPanel(String title, JLabel valueLabel, Color color) {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0, 0, 0, 30), 1),
            BorderFactory.createEmptyBorder(0, 0, 0, 0)
        ));

        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBackground(color);
        panel.setBorder(BorderFactory.createEmptyBorder(25, 25, 25, 25));

        panel.setPreferredSize(new Dimension(250, 180));

        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 22));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        panel.add(Box.createVerticalGlue());
        panel.add(titleLabel);
        panel.add(Box.createRigidArea(new Dimension(0, 15)));
        panel.add(valueLabel);
        panel.add(Box.createVerticalGlue());

        mainPanel.add(panel, BorderLayout.CENTER);

        mainPanel.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                panel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
                panel.setBackground(color.brighter());
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                panel.setBorder(BorderFactory.createEmptyBorder(25, 25, 25, 25));
                panel.setBackground(color);
            }
        });

        return mainPanel;
    }
}
