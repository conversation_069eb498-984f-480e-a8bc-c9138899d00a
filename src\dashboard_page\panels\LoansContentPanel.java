package dashboard_page.panels;

import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.*;
import java.text.SimpleDateFormat;
import library.management.system.database.DatabaseConnection;


public class LoansContentPanel extends JPanel {

    // Static instance for access from other panels
    private static LoansContentPanel instance;

    private JTable loansTable;
    private JTextField searchField;
    private JButton searchButton;
    private JButton renewButton;
    private JButton refreshButton;
    private JButton approveButton;
    private JButton declineButton;
    private JButton returnButton; // Add this field declaration with the other buttons
    private DefaultTableModel tableModel;
    private JComboBox<String> statusFilterComboBox;

    /**
     * Static method to refresh the loans data from other panels
     */
    public static void refreshData() {
        if (instance != null) {
            instance.loadLoansData();
        }
    }

    // Status options for loans
    private static final String[] LOAN_STATUSES = {
        "All", "Pending", "Approved", "Declined", "Borrowed", "Overdue", "Returned"
    };

    public LoansContentPanel() {
        initComponents();
        loadLoansData();
        instance = this;
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));
        setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // Add search panel at the top
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));

        searchField = new JTextField(20);
        // Add key listener to search when Enter is pressed
        searchField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyPressed(java.awt.event.KeyEvent e) {
                if (e.getKeyCode() == java.awt.event.KeyEvent.VK_ENTER) {
                    searchLoans();
                }
            }
        });

        searchButton = new JButton("Search");
        searchButton.setFont(new Font("Arial", Font.BOLD, 12));
        searchButton.setBackground(new Color(70, 130, 180)); // Steel blue
        searchButton.setForeground(Color.WHITE);
        searchButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                searchLoans();
            }
        });

        // Add a clear button
        JButton clearButton = new JButton("Clear");
        clearButton.setFont(new Font("Arial", Font.BOLD, 12));
        clearButton.setBackground(new Color(180, 180, 180)); // Light gray
        clearButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                searchField.setText("");
                loadLoansData(); // Reload all data
            }
        });

        // Add status filter
        JPanel filterPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        filterPanel.add(new JLabel("Status:"));
        statusFilterComboBox = new JComboBox<>(LOAN_STATUSES);
        statusFilterComboBox.setPreferredSize(new Dimension(120, 25));
        statusFilterComboBox.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                loadLoansData();
            }
        });
        filterPanel.add(statusFilterComboBox);

        // Create a panel to hold both search and filter
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.add(filterPanel, BorderLayout.WEST);
        topPanel.add(searchPanel, BorderLayout.EAST);

        searchPanel.add(new JLabel("Search:"));
        searchPanel.add(searchField);
        searchPanel.add(searchButton);
        searchPanel.add(clearButton);

        add(topPanel, BorderLayout.NORTH);

        // Create the table
        String[] columns = {"Loan ID", "Book Title", "Borrower", "Issue Date", "Due Date", "Return Date", "Status"};
        tableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Don't allow editing any cells
            }
        };

        loansTable = new JTable(tableModel);
        loansTable.setFillsViewportHeight(true);
        loansTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // Increase row height for better readability
        loansTable.setRowHeight(35);

        // Improve table appearance
        loansTable.setShowGrid(true);
        loansTable.setGridColor(new Color(200, 200, 200));
        loansTable.setFont(new Font("Arial", Font.PLAIN, 12));

        // Style the table header
        JTableHeader header = loansTable.getTableHeader();
        header.setFont(new Font("Arial", Font.BOLD, 14));
        header.setBackground(new Color(220, 220, 220)); // Light gray background
        header.setForeground(new Color(50, 50, 50)); // Dark gray text
        header.setPreferredSize(new Dimension(header.getWidth(), 40)); // Increase header height

        // Create a custom header renderer to ensure text is visible
        ((DefaultTableCellRenderer)header.getDefaultRenderer()).setHorizontalAlignment(JLabel.CENTER);

        // Set column widths for better readability
        loansTable.getColumnModel().getColumn(0).setPreferredWidth(60);  // Loan ID
        loansTable.getColumnModel().getColumn(1).setPreferredWidth(200); // Book Title
        loansTable.getColumnModel().getColumn(2).setPreferredWidth(150); // Borrower
        loansTable.getColumnModel().getColumn(3).setPreferredWidth(100); // Issue Date
        loansTable.getColumnModel().getColumn(4).setPreferredWidth(100); // Due Date
        loansTable.getColumnModel().getColumn(5).setPreferredWidth(100); // Return Date
        loansTable.getColumnModel().getColumn(6).setPreferredWidth(100);  // Status

        // Set up alternating row colors for better readability
        loansTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (!isSelected) {
                    c.setBackground(row % 2 == 0 ? new Color(245, 245, 245) : Color.WHITE);
                    c.setForeground(new Color(50, 50, 50)); // Dark gray text
                } else {
                    c.setBackground(new Color(230, 230, 250)); // Light purple for selection
                    c.setForeground(new Color(50, 50, 50)); // Dark gray text
                }

                // Add some padding to cell contents
                if (c instanceof JLabel) {
                    JLabel label = (JLabel) c;
                    label.setBorder(BorderFactory.createEmptyBorder(0, 8, 0, 8)); // Increased padding

                    // Center-align numeric columns and status
                    if (column == 0) { // Loan ID
                        label.setHorizontalAlignment(JLabel.CENTER);
                    } else if (column == 6) { // Status column (now column 6 after removing Librarian)
                        label.setHorizontalAlignment(JLabel.CENTER);

                        // Color-code status
                        if (value != null) {
                            String status = value.toString();
                            if ("Borrowed".equals(status)) {
                                label.setForeground(new Color(0, 128, 0)); // Green
                            } else if ("Overdue".equals(status)) {
                                label.setForeground(new Color(220, 0, 0)); // Red
                            } else if ("Returned".equals(status)) {
                                label.setForeground(new Color(0, 0, 220)); // Blue
                            } else if ("Pending".equals(status)) {
                                label.setForeground(new Color(255, 165, 0)); // Orange
                            } else if ("Approved".equals(status)) {
                                label.setForeground(new Color(75, 0, 130)); // Indigo
                            } else if ("Declined".equals(status)) {
                                label.setForeground(new Color(128, 0, 0)); // Maroon
                            }
                        }
                    } else {
                        label.setHorizontalAlignment(JLabel.LEFT);
                    }
                }

                return c;
            }
        });

        // Add mouse listener for row selection
        loansTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int selectedRow = loansTable.getSelectedRow();
                if (selectedRow >= 0) {
                    String status = (String) loansTable.getValueAt(selectedRow, 6); // Status column

                    // Enable/disable buttons based on status
                    if ("Pending".equals(status)) {
                        approveButton.setEnabled(true);
                        declineButton.setEnabled(true);
                        renewButton.setEnabled(false);
                        returnButton.setEnabled(false);
                    } else if ("Borrowed".equals(status) || "Overdue".equals(status)) {
                        approveButton.setEnabled(false);
                        declineButton.setEnabled(false);
                        renewButton.setEnabled(true);
                        returnButton.setEnabled(true); // Enable return button for borrowed/overdue books
                    } else {
                        approveButton.setEnabled(false);
                        declineButton.setEnabled(false);
                        renewButton.setEnabled(false);
                        returnButton.setEnabled(false);
                    }
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(loansTable);
        add(scrollPane, BorderLayout.CENTER);

        // Create the button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 10));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        buttonPanel.setBackground(new Color(240, 240, 240)); // Light gray background

        // Create refresh button
        refreshButton = new JButton("Refresh");
        refreshButton.setFont(new Font("Arial", Font.BOLD, 14));
        refreshButton.setBackground(new Color(100, 181, 246)); // Light blue background
        refreshButton.setForeground(Color.WHITE); // White text
        refreshButton.setFocusPainted(false);
        refreshButton.setPreferredSize(new Dimension(120, 40));
        refreshButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                loadLoansData();
            }
        });

        // Create approve button
        approveButton = new JButton("Approve Loan");
        approveButton.setFont(new Font("Arial", Font.BOLD, 14));
        approveButton.setBackground(new Color(46, 204, 113)); // Green background
        approveButton.setForeground(Color.WHITE); // White text
        approveButton.setFocusPainted(false);
        approveButton.setPreferredSize(new Dimension(140, 40));
        approveButton.setEnabled(false);
        approveButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                approveLoan();
            }
        });

        // Create decline button
        declineButton = new JButton("Decline Loan");
        declineButton.setFont(new Font("Arial", Font.BOLD, 14));
        declineButton.setBackground(new Color(231, 76, 60)); // Red background
        declineButton.setForeground(Color.WHITE); // White text
        declineButton.setFocusPainted(false);
        declineButton.setPreferredSize(new Dimension(140, 40));
        declineButton.setEnabled(false);
        declineButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                declineLoan();
            }
        });



        renewButton = new JButton("Renew Loan");
        renewButton.setFont(new Font("Arial", Font.BOLD, 14));
        renewButton.setBackground(new Color(155, 89, 182)); // Purple background
        renewButton.setForeground(Color.WHITE); // White text
        renewButton.setFocusPainted(false);
        renewButton.setPreferredSize(new Dimension(120, 40));
        renewButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                // This will be implemented later
                JOptionPane.showMessageDialog(LoansContentPanel.this,
                    "Renew Loan functionality will be implemented later",
                    "Renew Loan", JOptionPane.INFORMATION_MESSAGE);
            }
        });

        // Create return button
        returnButton = new JButton("Return Book");
        returnButton.setFont(new Font("Arial", Font.BOLD, 14));
        returnButton.setBackground(new Color(41, 128, 185)); // Blue background
        returnButton.setForeground(Color.WHITE); // White text
        returnButton.setFocusPainted(false);
        returnButton.setPreferredSize(new Dimension(140, 40));
        returnButton.setEnabled(false);
        returnButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                returnBook();
            }
        });

        // Disable buttons initially
        renewButton.setEnabled(false);
        approveButton.setEnabled(false);
        declineButton.setEnabled(false);
        returnButton.setEnabled(false);

        // Add all buttons to panel - make sure to add the return button
        buttonPanel.add(refreshButton);
        buttonPanel.add(approveButton);
        buttonPanel.add(declineButton);
        buttonPanel.add(renewButton);
        buttonPanel.add(returnButton); // Add the return button to the panel

        add(buttonPanel, BorderLayout.SOUTH);
    }

    /**
     * Approve a loan request
     */
    private void approveLoan() {
        int selectedRow = loansTable.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this,
                "Please select a loan to approve",
                "No Selection",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Convert view row index to model row index in case table is sorted
        int modelRow = loansTable.convertRowIndexToModel(selectedRow);

        // Get the loan ID and status
        int loanId = Integer.parseInt(tableModel.getValueAt(modelRow, 0).toString());
        String bookTitle = tableModel.getValueAt(modelRow, 1).toString();
        String status = tableModel.getValueAt(modelRow, 6).toString(); // Status column (now column 6 after removing Librarian)

        // Check if the loan is in Pending status
        if (!"Pending".equals(status)) {
            JOptionPane.showMessageDialog(this,
                "Only pending loans can be approved",
                "Invalid Status",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Confirm approval
        int confirm = JOptionPane.showConfirmDialog(this,
            "Are you sure you want to approve the loan request for \"" + bookTitle + "\"?",
            "Confirm Approval",
            JOptionPane.YES_NO_OPTION);

        if (confirm != JOptionPane.YES_OPTION) {
            return;
        }

        try {
            Connection conn = DatabaseConnection.getConnection();

            // First, get the copy ID and book ID from the loan
            String getCopyQuery = "SELECT l.CopyID, bc.BookID, bc.Copies FROM Loans l " +
                                 "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                                 "WHERE l.LoanID = ?";
            PreparedStatement getCopyStmt = conn.prepareStatement(getCopyQuery);
            getCopyStmt.setInt(1, loanId);
            ResultSet copyRs = getCopyStmt.executeQuery();

            if (!copyRs.next()) {
                JOptionPane.showMessageDialog(this,
                    "Could not find the loan record",
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
                copyRs.close();
                getCopyStmt.close();
                DatabaseConnection.closeConnection(conn);
                return;
            }

            int copyId = copyRs.getInt("CopyID");
            int currentCopies = copyRs.getInt("Copies");
            copyRs.close();
            getCopyStmt.close();

            // Check if there are available copies
            if (currentCopies <= 0) {
                JOptionPane.showMessageDialog(this,
                    "Cannot approve loan: No copies available for this book.",
                    "No Copies Available",
                    JOptionPane.WARNING_MESSAGE);
                DatabaseConnection.closeConnection(conn);
                return;
            }

            // Update the loan status to 'Approved'
            String updateLoanQuery = "UPDATE Loans SET Status = 'Approved' WHERE LoanID = ?";
            PreparedStatement updateLoanStmt = conn.prepareStatement(updateLoanQuery);
            updateLoanStmt.setInt(1, loanId);
            int loanRowsAffected = updateLoanStmt.executeUpdate();
            updateLoanStmt.close();

            // Update the copy status to 'Loaned' and decrement the copies count
            String updateCopyQuery = "UPDATE BookCopies SET Status = 'Loaned', Copies = Copies - 1 WHERE CopyID = ?";
            PreparedStatement updateCopyStmt = conn.prepareStatement(updateCopyQuery);
            updateCopyStmt.setInt(1, copyId);
            int copyRowsAffected = updateCopyStmt.executeUpdate();
            updateCopyStmt.close();

            if (loanRowsAffected > 0 && copyRowsAffected > 0) {
                JOptionPane.showMessageDialog(this,
                    "Loan for \"" + bookTitle + "\" has been approved successfully",
                    "Approval Successful",
                    JOptionPane.INFORMATION_MESSAGE);

                // Refresh the loans table
                loadLoansData();

                // Refresh the book copies panel to show updated status
                dashboard_page.panels.BookCopiesContentPanel.refreshData();
            } else {
                JOptionPane.showMessageDialog(this,
                    "Failed to approve loan. Please try again.",
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error approving loan: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Decline a loan request
     */
    private void declineLoan() {
        int selectedRow = loansTable.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this,
                "Please select a loan to decline",
                "No Selection",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Convert view row index to model row index in case table is sorted
        int modelRow = loansTable.convertRowIndexToModel(selectedRow);

        // Get the loan ID and status
        int loanId = Integer.parseInt(tableModel.getValueAt(modelRow, 0).toString());
        String bookTitle = tableModel.getValueAt(modelRow, 1).toString();
        String status = tableModel.getValueAt(modelRow, 6).toString(); // Status column (now column 6 after removing Librarian)

        // Check if the loan is in Pending status
        if (!"Pending".equals(status)) {
            JOptionPane.showMessageDialog(this,
                "Only pending loans can be declined",
                "Invalid Status",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Confirm decline
        int confirm = JOptionPane.showConfirmDialog(this,
            "Are you sure you want to decline the loan request for \"" + bookTitle + "\"?",
            "Confirm Decline",
            JOptionPane.YES_NO_OPTION);

        if (confirm != JOptionPane.YES_OPTION) {
            return;
        }

        try {
            Connection conn = DatabaseConnection.getConnection();

            // Update the loan status to 'Declined'
            String updateQuery = "UPDATE Loans SET Status = 'Declined' WHERE LoanID = ?";
            PreparedStatement updateStmt = conn.prepareStatement(updateQuery);
            updateStmt.setInt(1, loanId);
            int rowsAffected = updateStmt.executeUpdate();
            updateStmt.close();

            if (rowsAffected > 0) {
                JOptionPane.showMessageDialog(this,
                    "Loan request for \"" + bookTitle + "\" has been declined",
                    "Decline Successful",
                    JOptionPane.INFORMATION_MESSAGE);

                // Refresh the loans table
                loadLoansData();
            } else {
                JOptionPane.showMessageDialog(this,
                    "Failed to decline loan request. Please try again.",
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error declining loan: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Load all loans data from the database
     */
    private void loadLoansData() {
        try {
            // Clear the table
            tableModel.setRowCount(0);

            // Get the selected status filter
            String statusFilter = (String) statusFilterComboBox.getSelectedItem();

            Connection conn = DatabaseConnection.getConnection();

            // Build the query based on the status filter
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("SELECT l.LoanID, b.Title, u.FullName as Borrower, lib.FullName as Librarian, ")
                       .append("l.LoanDate, l.DueDate, l.ReturnDate, l.Status, ")
                       .append("CASE ")
                       .append("  WHEN l.ReturnDate IS NOT NULL THEN 'Returned' ")
                       .append("  WHEN l.Status = 'Pending' THEN 'Pending' ")
                       .append("  WHEN l.Status = 'Approved' THEN 'Approved' ")
                       .append("  WHEN l.Status = 'Declined' THEN 'Declined' ")
                       .append("  WHEN l.DueDate < CURRENT_DATE THEN 'Overdue' ")
                       .append("  ELSE 'Borrowed' ")
                       .append("END as DisplayStatus ")
                       .append("FROM Loans l ")
                       .append("JOIN BookCopies bc ON l.CopyID = bc.CopyID ")
                       .append("JOIN Books b ON bc.BookID = b.BookID ")
                       .append("JOIN Users u ON l.UserID = u.UserID ")
                       .append("JOIN Users lib ON l.IssuedByUserID = lib.UserID ");

            // Add status filter if not "All"
            if (!"All".equals(statusFilter)) {
                if ("Pending".equals(statusFilter)) {
                    queryBuilder.append("WHERE l.Status = 'Pending' ");
                } else if ("Approved".equals(statusFilter)) {
                    queryBuilder.append("WHERE l.Status = 'Approved' ");
                } else if ("Declined".equals(statusFilter)) {
                    queryBuilder.append("WHERE l.Status = 'Declined' ");
                } else if ("Borrowed".equals(statusFilter)) {
                    queryBuilder.append("WHERE l.ReturnDate IS NULL AND l.DueDate >= CURRENT_DATE AND l.Status = 'Approved' ");
                } else if ("Overdue".equals(statusFilter)) {
                    queryBuilder.append("WHERE l.ReturnDate IS NULL AND l.DueDate < CURRENT_DATE AND l.Status = 'Approved' ");
                } else if ("Returned".equals(statusFilter)) {
                    queryBuilder.append("WHERE l.ReturnDate IS NOT NULL ");
                }
            }

            queryBuilder.append("ORDER BY l.LoanDate DESC");

            PreparedStatement stmt = conn.prepareStatement(queryBuilder.toString());
            ResultSet rs = stmt.executeQuery();

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

            while (rs.next()) {
                int loanId = rs.getInt("LoanID");
                String title = rs.getString("Title");
                String borrower = rs.getString("Borrower");
                // Librarian column is not displayed in the UI
                Date loanDate = rs.getDate("LoanDate");
                Date dueDate = rs.getDate("DueDate");
                Date returnDate = rs.getDate("ReturnDate");
                String displayStatus = rs.getString("DisplayStatus");

                tableModel.addRow(new Object[]{
                    loanId,
                    title,
                    borrower,
                    loanDate != null ? dateFormat.format(loanDate) : "",
                    dueDate != null ? dateFormat.format(dueDate) : "",
                    returnDate != null ? dateFormat.format(returnDate) : "",
                    displayStatus
                });
            }

            rs.close();
            stmt.close();
            DatabaseConnection.closeConnection(conn);

            // No need to show a message dialog when no loans are found

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error loading loans data: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Search for loans based on the search term
     */
    private void searchLoans() {
        String searchTerm = searchField.getText().trim();
        if (searchTerm.isEmpty()) {
            // If search term is empty, show all loans
            loadLoansData();
            return;
        }

        try {
            // Clear the table
            tableModel.setRowCount(0);

            Connection conn = DatabaseConnection.getConnection();

            // Build the query to search by book title or borrower name
            String query = "SELECT l.LoanID, b.Title, u.FullName as Borrower, lib.FullName as Librarian, " +
                          "l.LoanDate, l.DueDate, l.ReturnDate, l.Status, " +
                          "CASE " +
                          "  WHEN l.ReturnDate IS NOT NULL THEN 'Returned' " +
                          "  WHEN l.Status = 'Pending' THEN 'Pending' " +
                          "  WHEN l.Status = 'Approved' THEN 'Approved' " +
                          "  WHEN l.Status = 'Declined' THEN 'Declined' " +
                          "  WHEN l.DueDate < CURRENT_DATE THEN 'Overdue' " +
                          "  ELSE 'Borrowed' " +
                          "END as DisplayStatus " +
                          "FROM Loans l " +
                          "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                          "JOIN Books b ON bc.BookID = b.BookID " +
                          "JOIN Users u ON l.UserID = u.UserID " +
                          "JOIN Users lib ON l.IssuedByUserID = lib.UserID " +
                          "WHERE LOWER(b.Title) LIKE ? OR LOWER(u.FullName) LIKE ? " +
                          "ORDER BY l.LoanDate DESC";

            PreparedStatement stmt = conn.prepareStatement(query);
            stmt.setString(1, "%" + searchTerm.toLowerCase() + "%");
            stmt.setString(2, "%" + searchTerm.toLowerCase() + "%");

            ResultSet rs = stmt.executeQuery();

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

            while (rs.next()) {
                int loanId = rs.getInt("LoanID");
                String title = rs.getString("Title");
                String borrower = rs.getString("Borrower");
                // Librarian column is not displayed in the UI
                Date loanDate = rs.getDate("LoanDate");
                Date dueDate = rs.getDate("DueDate");
                Date returnDate = rs.getDate("ReturnDate");
                String displayStatus = rs.getString("DisplayStatus");

                tableModel.addRow(new Object[]{
                    loanId,
                    title,
                    borrower,
                    loanDate != null ? dateFormat.format(loanDate) : "",
                    dueDate != null ? dateFormat.format(dueDate) : "",
                    returnDate != null ? dateFormat.format(returnDate) : "",
                    displayStatus
                });
            }

            rs.close();
            stmt.close();
            DatabaseConnection.closeConnection(conn);

            if (tableModel.getRowCount() == 0) {
                // If no loans found, show a message
                JOptionPane.showMessageDialog(this,
                    "No loans found matching your search.",
                    "No Results",
                    JOptionPane.INFORMATION_MESSAGE);
            }

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error searching loans: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Process a book return
     */
    private void returnBook() {
        int selectedRow = loansTable.getSelectedRow();
        if (selectedRow < 0) {
            JOptionPane.showMessageDialog(this,
                "Please select a loan to process return",
                "No Selection",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Convert view row index to model row index in case table is sorted
        int modelRow = loansTable.convertRowIndexToModel(selectedRow);

        // Get the loan ID and book title
        int loanId = Integer.parseInt(tableModel.getValueAt(modelRow, 0).toString());
        String bookTitle = tableModel.getValueAt(modelRow, 1).toString();
        String borrowerName = tableModel.getValueAt(modelRow, 2).toString();
        String status = tableModel.getValueAt(modelRow, 6).toString();

        // Check if the loan is in a returnable status
        if (!"Borrowed".equals(status) && !"Overdue".equals(status)) {
            JOptionPane.showMessageDialog(this,
                "Only books with 'Borrowed' or 'Overdue' status can be returned",
                "Invalid Status",
                JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Confirm return
        int confirm = JOptionPane.showConfirmDialog(this,
            "Are you sure you want to process the return of \"" + bookTitle + "\" from " + borrowerName + "?",
            "Confirm Return",
            JOptionPane.YES_NO_OPTION);

        if (confirm != JOptionPane.YES_OPTION) {
            return;
        }

        try {
            Connection conn = DatabaseConnection.getConnection();

            // First, get the copy ID
            String getCopyQuery = "SELECT CopyID FROM Loans WHERE LoanID = ?";
            PreparedStatement getCopyStmt = conn.prepareStatement(getCopyQuery);
            getCopyStmt.setInt(1, loanId);
            ResultSet copyRs = getCopyStmt.executeQuery();

            if (!copyRs.next()) {
                JOptionPane.showMessageDialog(this,
                    "Could not find the loan record",
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
                copyRs.close();
                getCopyStmt.close();
                DatabaseConnection.closeConnection(conn);
                return;
            }

            int copyId = copyRs.getInt("CopyID");
            copyRs.close();
            getCopyStmt.close();

            // Update the loan record with return date
            String updateLoanQuery = "UPDATE Loans SET ReturnDate = CURRENT_DATE WHERE LoanID = ?";
            PreparedStatement updateLoanStmt = conn.prepareStatement(updateLoanQuery);
            updateLoanStmt.setInt(1, loanId);
            int loanRowsAffected = updateLoanStmt.executeUpdate();
            updateLoanStmt.close();

            // Update the copy status back to Available and increment the copies count
            String updateCopyQuery = "UPDATE BookCopies SET Status = 'Available', Copies = Copies + 1 WHERE CopyID = ?";
            PreparedStatement updateCopyStmt = conn.prepareStatement(updateCopyQuery);
            updateCopyStmt.setInt(1, copyId);
            int copyRowsAffected = updateCopyStmt.executeUpdate();
            updateCopyStmt.close();

            if (loanRowsAffected > 0 && copyRowsAffected > 0) {
                // Check if the book was overdue
                boolean wasOverdue = "Overdue".equals(status);
                String successMessage = "Book \"" + bookTitle + "\" has been returned successfully.";
                
                if (wasOverdue) {
                    successMessage += "\nNote: This book was returned past its due date.";
                }
                
                JOptionPane.showMessageDialog(this,
                    successMessage,
                    "Return Successful",
                    JOptionPane.INFORMATION_MESSAGE);

                // Refresh the loans table
                loadLoansData();

                // Refresh the book copies panel to show updated status
                dashboard_page.panels.BookCopiesContentPanel.refreshData();
                
                // Also refresh the user's loans panel if it exists
                try {
                    // Call the static method properly
                    dashboard_page.panels.MyLoansContentPanel.refreshData();
                } catch (Exception ex) {
                    // Just log the error but don't interrupt the return process
                    System.err.println("Could not refresh MyLoansContentPanel: " + ex.getMessage());
                }
            } else {
                JOptionPane.showMessageDialog(this,
                    "Failed to process book return. Please try again.",
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error processing book return: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }
}
