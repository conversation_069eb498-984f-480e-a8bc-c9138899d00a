package library.management.system.database;

import java.sql.Connection;
import java.sql.SQLException;
import javax.swing.JOptionPane;

/**
 * Test class for database connection issues
 */
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("Testing database connection...");
        
        try {
            // First, try to shut down any existing connections
            DatabaseConnection.shutdownDatabase();
            
            // Wait a moment
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // Ignore
            }
            
            // Now try to connect
            Connection conn = DatabaseConnection.getConnection();
            System.out.println("Connection successful!");
            
            // Close the connection
            DatabaseConnection.closeConnection(conn);
            
            // Shut down the database
            DatabaseConnection.shutdownDatabase();
            
            JOptionPane.showMessageDialog(null, 
                "Database connection test successful!", 
                "Success", 
                JOptionPane.INFORMATION_MESSAGE);
            
        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
            
            JOptionPane.showMessageDialog(null, 
                "Database connection test failed: " + e.getMessage(), 
                "Error", 
                JOptionPane.ERROR_MESSAGE);
        } catch (ClassNotFoundException e) {
            System.err.println("Driver not found: " + e.getMessage());
            e.printStackTrace();
            
            JOptionPane.showMessageDialog(null, 
                "Database driver not found: " + e.getMessage(), 
                "Error", 
                JOptionPane.ERROR_MESSAGE);
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e.getMessage());
            e.printStackTrace();
            
            JOptionPane.showMessageDialog(null, 
                "Unexpected error: " + e.getMessage(), 
                "Error", 
                JOptionPane.ERROR_MESSAGE);
        }
        
        System.exit(0);
    }
}
