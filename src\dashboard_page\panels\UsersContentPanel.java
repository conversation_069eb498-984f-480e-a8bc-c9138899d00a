package dashboard_page.panels;

import forms.AddUserFRM;
import forms.EditUserFRM;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.sql.*;
import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;
import javax.swing.table.TableRowSorter;
import library.management.system.database.DatabaseConnection;

public class UsersContentPanel extends JPanel {

    private JTable usersTable;
    private DefaultTableModel tableModel;
    private JTextField searchField;
    private JButton searchButton;
    private JButton clearButton;
    private JButton refreshButton;
    private JButton addButton;
    private JButton editButton;
    private JButton deleteButton;

    public UsersContentPanel() {
        initComponents();
        loadUsersData();
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));
        setBackground(new Color(240, 240, 240));

        // Create header panel with title
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(240, 240, 240));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 10, 20));

        JLabel titleLabel = new JLabel("User Management");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        headerPanel.add(titleLabel, BorderLayout.WEST);

        // Create search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchPanel.setBackground(new Color(240, 240, 240));

        searchField = new JTextField(20);
        searchButton = new JButton("Search");
        clearButton = new JButton("Clear");

        searchPanel.add(new JLabel("Search:"));
        searchPanel.add(searchField);
        searchPanel.add(searchButton);
        searchPanel.add(clearButton);

        headerPanel.add(searchPanel, BorderLayout.EAST);

        // Create table
        String[] columns = {"User ID", "Username", "Full Name", "Email", "Phone Number", "Role"};
        tableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // Make table non-editable
            }
        };

        usersTable = new JTable(tableModel);
        usersTable.setRowHeight(30); // Set row height
        usersTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        usersTable.setAutoCreateRowSorter(true);

        // Style the table header
        JTableHeader header = usersTable.getTableHeader();
        header.setBackground(new Color(51, 102, 255));
        header.setForeground(Color.WHITE);
        header.setFont(new Font("Arial", Font.BOLD, 14));

        // Add table to scroll pane
        JScrollPane scrollPane = new JScrollPane(usersTable);
        scrollPane.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));

        // Create button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setBackground(new Color(240, 240, 240));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(10, 20, 20, 20));

        refreshButton = new JButton("Refresh");
        addButton = new JButton("Add User");
        editButton = new JButton("Edit User");
        deleteButton = new JButton("Delete User");

        buttonPanel.add(refreshButton);
        buttonPanel.add(addButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);

        // Add components to panel
        add(headerPanel, BorderLayout.NORTH);
        add(scrollPane, BorderLayout.CENTER);
        add(buttonPanel, BorderLayout.SOUTH);

        // Set up action listeners
        setupActionListeners();
    }

    private void setupActionListeners() {
        // Search button
        searchButton.addActionListener((ActionEvent e) -> {
            String searchTerm = searchField.getText().trim();
            if (!searchTerm.isEmpty()) {
                TableRowSorter<DefaultTableModel> sorter = new TableRowSorter<>(tableModel);
                usersTable.setRowSorter(sorter);
                sorter.setRowFilter(RowFilter.regexFilter("(?i)" + searchTerm));
            }
        });

        // Clear button
        clearButton.addActionListener((ActionEvent e) -> {
            searchField.setText("");
            usersTable.setRowSorter(null);
        });

        // Refresh button
        refreshButton.addActionListener((ActionEvent e) -> {
            loadUsersData();
        });

        // Add button
        addButton.addActionListener((ActionEvent e) -> {
            AddUserFRM addUserForm = new AddUserFRM();
            addUserForm.setLocationRelativeTo(this);
            addUserForm.setVisible(true);

            // Refresh data after form is closed
            addUserForm.addWindowListener(new java.awt.event.WindowAdapter() {
                @Override
                public void windowClosed(java.awt.event.WindowEvent windowEvent) {
                    loadUsersData();
                }
            });
        });

        // Edit button
        editButton.addActionListener((ActionEvent e) -> {
            int selectedRow = usersTable.getSelectedRow();
            if (selectedRow >= 0) {
                // Convert view index to model index in case table is sorted
                int modelRow = usersTable.convertRowIndexToModel(selectedRow);
                int userId = Integer.parseInt(tableModel.getValueAt(modelRow, 0).toString());

                EditUserFRM editUserForm = new EditUserFRM(userId);
                editUserForm.setLocationRelativeTo(this);
                editUserForm.setVisible(true);

                // Refresh data after form is closed
                editUserForm.addWindowListener(new java.awt.event.WindowAdapter() {
                    @Override
                    public void windowClosed(java.awt.event.WindowEvent windowEvent) {
                        loadUsersData();
                    }
                });
            } else {
                JOptionPane.showMessageDialog(this,
                    "Please select a user to edit",
                    "No Selection",
                    JOptionPane.INFORMATION_MESSAGE);
            }
        });

        // Delete button
        deleteButton.addActionListener((ActionEvent e) -> {
            int selectedRow = usersTable.getSelectedRow();
            if (selectedRow >= 0) {
                // Convert view index to model index in case table is sorted
                int modelRow = usersTable.convertRowIndexToModel(selectedRow);
                int userId = Integer.parseInt(tableModel.getValueAt(modelRow, 0).toString());
                String username = tableModel.getValueAt(modelRow, 1).toString();

                int confirm = JOptionPane.showConfirmDialog(
                    this,
                    "Are you sure you want to delete user '" + username + "'?",
                    "Confirm Deletion",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.WARNING_MESSAGE
                );

                if (confirm == JOptionPane.YES_OPTION) {
                    deleteUser(userId);
                }
            } else {
                JOptionPane.showMessageDialog(this,
                    "Please select a user to delete",
                    "No Selection",
                    JOptionPane.INFORMATION_MESSAGE);
            }
        });
    }

    /**
     * Load users data from the database
     */
    public void loadUsersData() {
        // Clear existing data
        tableModel.setRowCount(0);

        try {
            System.out.println("Attempting to load users data...");
            Connection conn = DatabaseConnection.getConnection();
            if (conn == null) {
                System.err.println("Database connection is null");
                return;
            }

            System.out.println("Database connection established");
            String query = "SELECT UserID, Username, FullName, Email, PhoneNumber, Role FROM Users ORDER BY UserID";

            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(query)) {

                System.out.println("Query executed successfully");
                int rowCount = 0;

                while (rs.next()) {
                    Object[] row = {
                        rs.getInt("UserID"),
                        rs.getString("Username"),
                        rs.getString("FullName"),
                        rs.getString("Email"),
                        rs.getString("PhoneNumber"),
                        rs.getString("Role")
                    };
                    tableModel.addRow(row);
                    rowCount++;
                }

                System.out.println("Loaded " + rowCount + " users");
            }

            DatabaseConnection.closeConnection(conn);
            System.out.println("Database connection closed");

        } catch (Exception e) {
            System.err.println("Error loading users data: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error loading users data: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    private void deleteUser(int userId) {
        try {
            Connection conn = DatabaseConnection.getConnection();

            // Check if user has any loans or fines
            boolean hasLoans = false;
            boolean hasFines = false;

            // Check for loans
            try (PreparedStatement checkLoans = conn.prepareStatement(
                    "SELECT COUNT(*) FROM Loans WHERE UserID = ?")) {
                checkLoans.setInt(1, userId);
                ResultSet loanRs = checkLoans.executeQuery();
                if (loanRs.next() && loanRs.getInt(1) > 0) {
                    hasLoans = true;
                }
            }

            // Check for fines
            try (PreparedStatement checkFines = conn.prepareStatement(
                    "SELECT COUNT(*) FROM Fines WHERE UserID = ?")) {
                checkFines.setInt(1, userId);
                ResultSet fineRs = checkFines.executeQuery();
                if (fineRs.next() && fineRs.getInt(1) > 0) {
                    hasFines = true;
                }
            }

            // If user has loans or fines, ask for confirmation to force delete
            if (hasLoans || hasFines) {
                StringBuilder message = new StringBuilder("This user has ");
                if (hasLoans) message.append("active loans");
                if (hasLoans && hasFines) message.append(" and ");
                if (hasFines) message.append("unpaid fines");
                message.append(".\nForce deleting will remove all related records.\nDo you want to proceed?");

                int confirm = JOptionPane.showConfirmDialog(
                    this,
                    message.toString(),
                    "Force Delete Confirmation",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.WARNING_MESSAGE
                );

                if (confirm != JOptionPane.YES_OPTION) {
                    DatabaseConnection.closeConnection(conn);
                    return;
                }

                // User confirmed force delete, proceed with deletion of related records
                conn.setAutoCommit(false); // Start transaction

                try {
                    // First delete fines
                    if (hasFines) {
                        try (PreparedStatement deleteFines = conn.prepareStatement(
                                "DELETE FROM Fines WHERE UserID = ?")) {
                            deleteFines.setInt(1, userId);
                            int finesDeleted = deleteFines.executeUpdate();
                            System.out.println("Deleted " + finesDeleted + " fines records");
                        }
                    }

                    // Then delete loans
                    if (hasLoans) {
                        // First, get all loans to update book copy status
                        try (PreparedStatement getLoans = conn.prepareStatement(
                                "SELECT LoanID, CopyID FROM Loans WHERE UserID = ?")) {
                            getLoans.setInt(1, userId);
                            ResultSet loansRs = getLoans.executeQuery();

                            // Update book copies status to Available
                            try (PreparedStatement updateCopy = conn.prepareStatement(
                                    "UPDATE BookCopies SET Status = 'Available' WHERE CopyID = ?")) {
                                while (loansRs.next()) {
                                    int copyId = loansRs.getInt("CopyID");
                                    updateCopy.setInt(1, copyId);
                                    updateCopy.executeUpdate();
                                }
                            }
                        }

                        // Now delete the loans
                        try (PreparedStatement deleteLoans = conn.prepareStatement(
                                "DELETE FROM Loans WHERE UserID = ?")) {
                            deleteLoans.setInt(1, userId);
                            int loansDeleted = deleteLoans.executeUpdate();
                            System.out.println("Deleted " + loansDeleted + " loans records");
                        }
                    }

                    // Finally delete the user
                    try (PreparedStatement deleteUser = conn.prepareStatement(
                            "DELETE FROM Users WHERE UserID = ?")) {
                        deleteUser.setInt(1, userId);
                        int usersDeleted = deleteUser.executeUpdate();

                        if (usersDeleted > 0) {
                            conn.commit(); // Commit transaction
                            JOptionPane.showMessageDialog(this,
                                "User and all related records deleted successfully",
                                "Success",
                                JOptionPane.INFORMATION_MESSAGE);

                            // Refresh the table
                            loadUsersData();
                        } else {
                            conn.rollback(); // Rollback if user not deleted
                            JOptionPane.showMessageDialog(this,
                                "Failed to delete user",
                                "Error",
                                JOptionPane.ERROR_MESSAGE);
                        }
                    }
                } catch (SQLException ex) {
                    // If any error occurs, rollback the transaction
                    try {
                        conn.rollback();
                    } catch (SQLException rollbackEx) {
                        System.err.println("Error rolling back transaction: " + rollbackEx.getMessage());
                    }
                    throw ex; // Re-throw the exception to be caught by the outer catch block
                } finally {
                    // Reset auto-commit mode
                    try {
                        conn.setAutoCommit(true);
                    } catch (SQLException autoCommitEx) {
                        System.err.println("Error resetting auto-commit: " + autoCommitEx.getMessage());
                    }
                }
            } else {
                // User has no loans or fines, proceed with simple deletion
                try (PreparedStatement pstmt = conn.prepareStatement("DELETE FROM Users WHERE UserID = ?")) {
                    pstmt.setInt(1, userId);
                    int rowsAffected = pstmt.executeUpdate();

                    if (rowsAffected > 0) {
                        JOptionPane.showMessageDialog(this,
                            "User deleted successfully",
                            "Success",
                            JOptionPane.INFORMATION_MESSAGE);

                        // Refresh the table
                        loadUsersData();
                    } else {
                        JOptionPane.showMessageDialog(this,
                            "Failed to delete user",
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                    }
                }
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            System.err.println("Error deleting user: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "Error deleting user: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }
}
