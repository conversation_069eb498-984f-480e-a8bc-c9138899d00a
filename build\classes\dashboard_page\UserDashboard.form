<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="undecorated" type="boolean" value="true"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="dashboardBTN" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="main_panel" min="-2" max="-2" attributes="0"/>
              <EmptySpace pref="1614" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="dashboardBTN" pref="1080" max="32767" attributes="0"/>
          <Group type="102" attributes="0">
              <Component id="main_panel" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="dashboardBTN">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="66" red="33" type="rgb"/>
        </Property>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[300, 0]"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="18" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Component id="userDashboardBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="myLoansBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="myFineBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="logoutBTN" min="-2" pref="264" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace pref="18" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="240" max="-2" attributes="0"/>
                  <Component id="userDashboardBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  <Component id="myLoansBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  <Component id="myFineBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace pref="560" max="32767" attributes="0"/>
                  <Component id="logoutBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JButton" name="userDashboardBTN">
          <Properties>
            <Property name="text" type="java.lang.String" value="Dashboard"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="myLoansBTN">
          <Properties>
            <Property name="text" type="java.lang.String" value="My Borrow"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="myFineBTN">
          <Properties>
            <Property name="text" type="java.lang.String" value="My Fines"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="logoutBTN">
          <Properties>
            <Property name="text" type="java.lang.String" value="Logout"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="main_panel">
      <Properties>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[0, 0]"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
    </Container>
  </SubComponents>
</Form>
