package forms;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Calendar;
import java.util.Properties;
import java.util.Date;
import javax.swing.*;
import java.awt.*;
import org.jdatepicker.impl.JDatePanelImpl;
import org.jdatepicker.impl.JDatePickerImpl;
import org.jdatepicker.impl.UtilDateModel;
import library.management.system.database.DatabaseConnection;

/**
 * A panel for adding new books to the database.
 */
public class AddBookPanel extends JPanel {
    // Callback interface for notifying parent when a book is added
    public interface BookAddedListener {
        void onBookAdded();
    }
    
    private BookAddedListener listener;
    
    // Form components
    private JTextField isbnField;
    private JTextField titleField;
    private JTextField authorField;
    private JTextField publisherField;
    private JTextField publicationYearField;
    private JTextField copyrightField;
    private JTextField genreField;
    private JTextField editionField;
    private JTextField copiesField;
    private JComboBox<String> statusComboBox;
    private JDatePickerImpl datePicker; // Changed from combo boxes to JDatePicker
    private JComboBox<String> conditionComboBox; // Changed from JTextField to JComboBox
    private JButton addButton;
    private JButton clearButton;
    
    /**
     * Creates a new AddBookPanel
     * @param listener Callback for when a book is successfully added
     */
    public AddBookPanel(BookAddedListener listener) {
        this.listener = listener;
        initComponents();
    }
    
    /**
     * Creates a new AddBookPanel without callback
     */
    public AddBookPanel() {
        this(null);
    }

    private void initComponents() {
        // Simple panel layout
        setLayout(new BorderLayout(10, 10));
        
        // Create header with simple title
        JPanel headerPanel = new JPanel();
        JLabel titleLabel = new JLabel("Add New Book");
        titleLabel.setFont(new Font(titleLabel.getFont().getName(), Font.BOLD, 16));
        headerPanel.add(titleLabel);
        
        // Main form panel using a simple layout
        JPanel formPanel = new JPanel();
        formPanel.setLayout(new BoxLayout(formPanel, BoxLayout.Y_AXIS));
        formPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        // Basic information panel
        JPanel basicInfoPanel = new JPanel(new GridLayout(0, 2, 5, 5));
        basicInfoPanel.setBorder(BorderFactory.createTitledBorder("Basic Information"));
        
        // Add form fields
        basicInfoPanel.add(new JLabel("ISBN:"));
        isbnField = new JTextField(20);
        basicInfoPanel.add(isbnField);
        
        basicInfoPanel.add(new JLabel("Title:"));
        titleField = new JTextField(20);
        basicInfoPanel.add(titleField);
        
        basicInfoPanel.add(new JLabel("Author:"));
        authorField = new JTextField(20);
        basicInfoPanel.add(authorField);
        
        // Publishing information panel
        JPanel publishingInfoPanel = new JPanel(new GridLayout(0, 2, 5, 5));
        publishingInfoPanel.setBorder(BorderFactory.createTitledBorder("Publishing Information"));
        
        publishingInfoPanel.add(new JLabel("Publisher:"));
        publisherField = new JTextField(20);
        publishingInfoPanel.add(publisherField);
        
        publishingInfoPanel.add(new JLabel("Publication Year:"));
        publicationYearField = new JTextField(20);
        publishingInfoPanel.add(publicationYearField);
        
        publishingInfoPanel.add(new JLabel("Copyright:"));
        copyrightField = new JTextField(20);
        publishingInfoPanel.add(copyrightField);
        
        publishingInfoPanel.add(new JLabel("Genre:"));
        genreField = new JTextField(20);
        publishingInfoPanel.add(genreField);
        
        publishingInfoPanel.add(new JLabel("Edition:"));
        editionField = new JTextField(20);
        publishingInfoPanel.add(editionField);
        
        // Catalog information panel
        JPanel catalogInfoPanel = new JPanel(new GridLayout(0, 2, 5, 5));
        catalogInfoPanel.setBorder(BorderFactory.createTitledBorder("Catalog Information"));
        
        catalogInfoPanel.add(new JLabel("Copies:"));
        copiesField = new JTextField("1"); // Default value
        catalogInfoPanel.add(copiesField);
        
        catalogInfoPanel.add(new JLabel("Status:"));
        statusComboBox = new JComboBox<>(new String[] {
            "Available", "Loaned", "Reserved", "Under Repair", "Lost"
        });
        catalogInfoPanel.add(statusComboBox);
        
        catalogInfoPanel.add(new JLabel("Acquisition Date:"));
        
        // Set up JDatePicker
        UtilDateModel model = new UtilDateModel();
        Properties p = new Properties();
        p.put("text.today", "Today");
        p.put("text.month", "Month");
        p.put("text.year", "Year");
        JDatePanelImpl datePanel = new JDatePanelImpl(model, p);
        datePicker = new JDatePickerImpl(datePanel, new DateLabelFormatter());
        catalogInfoPanel.add(datePicker);
        
        // Add condition field - this was missing
        catalogInfoPanel.add(new JLabel("Condition:"));
        conditionComboBox = new JComboBox<>(new String[] {
            "Good", "Acceptable", "Fair", "Poor"
        });
        conditionComboBox.setSelectedIndex(0); // Default to "Good"
        catalogInfoPanel.add(conditionComboBox);
        
        // Add panels to the form panel with some spacing
        formPanel.add(basicInfoPanel);
        formPanel.add(Box.createVerticalStrut(10));
        formPanel.add(publishingInfoPanel);
        formPanel.add(Box.createVerticalStrut(10));
        formPanel.add(catalogInfoPanel);
        
        // Button panel with simple layout
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        
        clearButton = new JButton("Clear");
        clearButton.addActionListener(e -> clearForm());
        
        addButton = new JButton("Save Book");
        addButton.addActionListener(e -> addBook());
        
        buttonPanel.add(clearButton);
        buttonPanel.add(addButton);
        
        // Add all panels to the main panel
        add(headerPanel, BorderLayout.NORTH);
        add(formPanel, BorderLayout.CENTER); // Remove JScrollPane wrapper
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    private void addBook() {
        // Get values from form fields
        String isbnValue = isbnField.getText().trim();
        String titleValue = titleField.getText().trim();
        String authorValue = authorField.getText().trim();
        String publisherValue = publisherField.getText().trim();
        String pubYearValue = publicationYearField.getText().trim();
        String copyrightValue = copyrightField.getText().trim();
        String genreValue = genreField.getText().trim();
        String editionValue = editionField.getText().trim();
        String copiesValue = copiesField.getText().trim();
        String statusValue = statusComboBox.getSelectedItem().toString();
        String conditionValue = conditionComboBox.getSelectedItem().toString();
        
        // Handle acquisition date from JDatePicker
        java.sql.Date acquisitionDate = null;
        if (datePicker.getModel().getValue() != null) {
            Date selectedDate = (Date) datePicker.getModel().getValue();
            acquisitionDate = new java.sql.Date(selectedDate.getTime());
        }
        
        // Validate required fields
        if (isbnValue.isEmpty() || titleValue.isEmpty() || authorValue.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "ISBN, Title, and Author are required fields.", 
                "Validation Error", 
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        try {
            // Connect to database
            Connection conn = DatabaseConnection.getConnection();

            String sql = "INSERT INTO Books (ISBN, Title, Author, Publisher, PublicationYear, Copyright, " +
                         "Genre, Edition, Copies, Status, AcquisitionDate, Condition) " +
                         "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, isbnValue);
            pstmt.setString(2, titleValue);
            pstmt.setString(3, authorValue);
            pstmt.setString(4, publisherValue);
            
            // Handle publication year
            if (!pubYearValue.isEmpty()) {
                try {
                    int pubYear = Integer.parseInt(pubYearValue);
                    pstmt.setInt(5, pubYear);
                } catch (NumberFormatException e) {
                    pstmt.setNull(5, java.sql.Types.INTEGER);
                }
            } else {
                pstmt.setNull(5, java.sql.Types.INTEGER);
            }
            
            pstmt.setString(6, copyrightValue);
            pstmt.setString(7, genreValue);
            pstmt.setString(8, editionValue);
            
            // Handle copies
            if (!copiesValue.isEmpty()) {
                try {
                    int copyCount = Integer.parseInt(copiesValue);
                    pstmt.setInt(9, copyCount);
                } catch (NumberFormatException e) {
                    pstmt.setInt(9, 0); // Default to 0 if invalid number
                }
            } else {
                pstmt.setInt(9, 0); // Default to 0 if empty
            }
            
            pstmt.setString(10, statusValue);
            
            // Set the acquisition date parameter
            if (acquisitionDate != null) {
                pstmt.setDate(11, acquisitionDate);
            } else {
                pstmt.setNull(11, java.sql.Types.DATE);
            }
            
            pstmt.setString(12, conditionValue);

            // Execute the insert
            int rows = pstmt.executeUpdate();
            
            if (rows > 0) {
                JOptionPane.showMessageDialog(this, 
                    "Book added successfully!", 
                    "Success", 
                    JOptionPane.INFORMATION_MESSAGE);
                
                // Notify listener if available
                if (listener != null) {
                    listener.onBookAdded();
                }
                
                // Clear form fields
                clearForm();
            } else {
                JOptionPane.showMessageDialog(this, 
                    "Failed to add book.", 
                    "Error", 
                    JOptionPane.ERROR_MESSAGE);
            }
            
            // Close resources
            pstmt.close();
            DatabaseConnection.closeConnection(conn);
            
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "Error adding book: " + e.getMessage(), 
                "Database Error", 
                JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }
    
    private void clearForm() {
        isbnField.setText("");
        titleField.setText("");
        authorField.setText("");
        publisherField.setText("");
        publicationYearField.setText("");
        copyrightField.setText("");
        genreField.setText("");
        editionField.setText("");
        copiesField.setText("1"); // Reset to default value
        statusComboBox.setSelectedIndex(0);
        
        // Reset date picker
        datePicker.getModel().setValue(null);
        
        conditionComboBox.setSelectedIndex(0); // Reset to "Good"
    }
    
    /**
     * Set a listener to be notified when a book is successfully added
     * @param listener The listener to set
     */
    public void setBookAddedListener(BookAddedListener listener) {
        this.listener = listener;
    }
}
