package library.management.system.login_register;

import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;
import library.management.system.database.DatabaseConnection;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.regex.Pattern;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Login extends javax.swing.JFrame {
    
    // Logger for this class
    private static final Logger LOGGER = Logger.getLogger(Login.class.getName());
    
    // Email validation pattern
    private static final Pattern EMAIL_PATTERN = 
        Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");
    
    // Maximum allowed length for inputs
    private static final int MAX_INPUT_LENGTH = 100;

    // Static variable to store the current user ID
    private static int currentUserId = 0;

    /**
     * Get the ID of the currently logged-in user
     * @return The user ID or 0 if no user is logged in
     */
    public static int getCurrentUserId() {
        return currentUserId;
    }

    public Login() {
        // Remove window decoration (title bar, minimize, maximize, close buttons)
        // Must be called before initComponents() makes the frame displayable
        setUndecorated(true);

        initComponents();
        password.setEchoChar('•');
        setLocationRelativeTo(null);
        
        // Initialize database before showing the frame
        initializeDatabase();
        
        setVisible(true);

        password.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    login_btnActionPerformed(null);
                }
            }
        });

        email.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    login_btnActionPerformed(null);
                }
            }
        });

        Runtime.getRuntime().addShutdownHook(new Thread() {
            @Override
            public void run() {
                DatabaseConnection.shutdownDatabase();
            }
        });
    }

    private void initComponents() {

        jPanel1 = new javax.swing.JPanel();
        jLabel1 = new javax.swing.JLabel();
        jPanel2 = new javax.swing.JPanel();
        login_btn = new javax.swing.JButton();
        register_btn = new javax.swing.JButton();
        exit_btn = new javax.swing.JButton();
        jLabel4 = new javax.swing.JLabel();
        jLabel2 = new javax.swing.JLabel();
        email = new javax.swing.JTextField();
        password = new javax.swing.JPasswordField();
        jLabel3 = new javax.swing.JLabel();
        show_pass = new javax.swing.JCheckBox();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        jPanel1.setBackground(new java.awt.Color(153, 153, 153));

        jLabel1.setFont(new java.awt.Font("Segoe UI", 1, 24));
        jLabel1.setText("LOGIN FORM");

        javax.swing.GroupLayout jPanel1Layout = new javax.swing.GroupLayout(jPanel1);
        jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel1Layout.createSequentialGroup()
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addComponent(jLabel1)
                .addGap(92, 92, 92))
        );
        jPanel1Layout.setVerticalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addGap(30, 30, 30)
                .addComponent(jLabel1)
                .addContainerGap(30, Short.MAX_VALUE))
        );

        jPanel2.setBackground(new java.awt.Color(153, 153, 153));

        login_btn.setText("Login");
        login_btn.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                login_btnActionPerformed(evt);
            }
        });

        register_btn.setText("Register");
        register_btn.setActionCommand("");
        register_btn.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                register_btnActionPerformed(evt);
            }
        });

        exit_btn.setText("Exit");
        exit_btn.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                exit_btnActionPerformed(evt);
            }
        });

        jLabel4.setFont(new java.awt.Font("Segoe UI", 1, 16));
        jLabel4.setText("Don't have an account?");



        javax.swing.GroupLayout jPanel2Layout = new javax.swing.GroupLayout(jPanel2);
        jPanel2.setLayout(jPanel2Layout);
        jPanel2Layout.setHorizontalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel2Layout.createSequentialGroup()
                .addGap(35, 35, 35)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(jLabel4)
                    .addComponent(login_btn, javax.swing.GroupLayout.PREFERRED_SIZE, 282, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(register_btn, javax.swing.GroupLayout.PREFERRED_SIZE, 282, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(exit_btn, javax.swing.GroupLayout.PREFERRED_SIZE, 282, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addContainerGap(20, Short.MAX_VALUE))
        );
        jPanel2Layout.setVerticalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel2Layout.createSequentialGroup()
                .addGap(30, 30, 30)
                .addComponent(login_btn)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                .addComponent(jLabel4)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(register_btn)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                .addComponent(exit_btn)
                .addContainerGap(30, Short.MAX_VALUE))
        );

        jLabel2.setText("Email");

        jLabel3.setText("Password");

        show_pass.setText("Show Password");
        show_pass.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                show_passActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addComponent(jPanel1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
            .addComponent(jPanel2, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
            .addGroup(layout.createSequentialGroup()
                .addGap(26, 26, 26)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(jLabel3)
                    .addComponent(jLabel2))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                    .addComponent(password, javax.swing.GroupLayout.DEFAULT_SIZE, 220, Short.MAX_VALUE)
                    .addComponent(email))
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addComponent(show_pass)
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(jPanel1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(45, 45, 45)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel2)
                    .addComponent(email, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(30, 30, 30)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel3)
                    .addComponent(password, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addComponent(show_pass)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addComponent(jPanel2, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addContainerGap())
        );

        pack();
    }

    private void show_passActionPerformed(java.awt.event.ActionEvent evt) {
        if (show_pass.isSelected()) {
            password.setEchoChar((char) 0);
        } else {
            password.setEchoChar('•');
        }
    }

    private void login_btnActionPerformed(java.awt.event.ActionEvent evt) {
        String userEmail = email.getText().trim();
        String userPassword = new String(password.getPassword());

        try {
            // Validate inputs
            if (!validateLoginInputs(userEmail, userPassword)) {
                return;
            }

            // Explicitly register Derby driver before establishing connection
            try {
                Class.forName("org.apache.derby.jdbc.EmbeddedDriver").newInstance();
                LOGGER.log(Level.INFO, "Explicitly registered Derby embedded driver");
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException e) {
                LOGGER.log(Level.SEVERE, "Failed to register Derby driver", e);
                JOptionPane.showMessageDialog(
                    this,
                    "Failed to initialize database driver.\nPlease make sure Derby JDBC driver is in your classpath.",
                    "Driver Error",
                    JOptionPane.ERROR_MESSAGE
                );
                return;
            }

            // Check database directory
            String dbPath = System.getProperty("user.dir") + File.separator + "lms";
            File dbDir = new File(dbPath);
            File parentDir = dbDir.getParentFile();
            
            if (!parentDir.exists() || !parentDir.canWrite()) {
                LOGGER.log(Level.SEVERE, "Database parent directory doesn't exist or isn't writable: {0}", parentDir.getPath());
                JOptionPane.showMessageDialog(
                    this,
                    "Cannot access or write to application directory.\nPlease run this application with proper permissions.",
                    "Permission Error",
                    JOptionPane.ERROR_MESSAGE
                );
                return;
            }

            Connection conn = null;
            try {
                // Try direct connection with specific URL format
                String jdbcUrl = "**************************";
                LOGGER.log(Level.INFO, "Connecting with URL: {0}", jdbcUrl);
                
                try {
                    conn = java.sql.DriverManager.getConnection(jdbcUrl);
                    LOGGER.log(Level.INFO, "Connection established successfully");
                } catch (SQLException directConnectEx) {
                    LOGGER.log(Level.WARNING, "Direct connection failed: {0}", directConnectEx.getMessage());
                    
                    // Fall back to DatabaseConnection class
                    conn = DatabaseConnection.getConnection();
                }
                
                String hashedPassword = hashPassword(userPassword);
                
                // Check if users table exists and has records
                int userCount = checkUserTableStatus(conn);
                
                if (userCount == 0) {
                    if (createDefaultAdmin(conn)) {
                        email.setText("admin");
                        password.setText("admin123");
                    } else {
                        return;
                    }
                }
                
                // Try to authenticate user
                if (authenticateUser(conn, userEmail, hashedPassword)) {
                    // Authentication was successful and handled in the method
                    return;
                }
                
                // If we get here, authentication failed
                showLoginFailedMessage();
            } catch (SQLException e) {
                if (e.getSQLState() != null && e.getSQLState().equals("08001")) {
                    LOGGER.log(Level.SEVERE, "No suitable driver found. Check Derby library installation", e);
                    JOptionPane.showMessageDialog(
                        this,
                        "Database driver not properly configured.\n" +
                        "Error: " + e.getMessage() + "\n\n" +
                        "Please ensure Derby JDBC driver is properly installed.",
                        "Driver Configuration Error",
                        JOptionPane.ERROR_MESSAGE
                    );
                } else {
                    handleSQLException(e);
                }
            } catch (ClassNotFoundException e) {
                LOGGER.log(Level.SEVERE, "Database driver not found", e);
                JOptionPane.showMessageDialog(
                    this,
                    "Database driver not found. Please make sure the Derby JDBC driver is installed.",
                    "Driver Error",
                    JOptionPane.ERROR_MESSAGE
                );
            } finally {
                if (conn != null) {
                    // Modified to handle connection closing without catching SQLException
                    // which might not be thrown by the closeConnection method
                    try {
                        DatabaseConnection.closeConnection(conn);
                    } catch (Exception e) {
                        // Catch a more general exception instead
                        LOGGER.log(Level.WARNING, "Error closing database connection", e);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Unexpected error during login", e);
            JOptionPane.showMessageDialog(
                this,
                "An unexpected error occurred: " + e.getMessage(),
                "System Error",
                JOptionPane.ERROR_MESSAGE
            );
        }
    }
    
    /**
     * Ensures the Derby JDBC driver is loaded
     * @return true if driver loaded successfully, false otherwise
     */
    private boolean ensureDerbyDriverLoaded() {
        try {
            // Check if driver is already registered
            try {
                Class.forName("org.apache.derby.jdbc.EmbeddedDriver");
                LOGGER.log(Level.INFO, "Derby driver is already loaded");
                return true;
            } catch (ClassNotFoundException e) {
                LOGGER.log(Level.WARNING, "Derby driver not found in classpath, attempting to load");
            }
            
            // Try alternative driver class names
            String[] driverClasses = {
                "org.apache.derby.jdbc.ClientDriver",
                "org.apache.derby.client.ClientAutoloadedDriver",
                "org.apache.derby.iapi.jdbc.AutoloadedDriver"
            };
            
            for (String driverClass : driverClasses) {
                try {
                    Class.forName(driverClass);
                    LOGGER.log(Level.INFO, "Successfully loaded Derby driver: {0}", driverClass);
                    return true;
                } catch (ClassNotFoundException e) {
                    LOGGER.log(Level.FINE, "Driver class not found: {0}", driverClass);
                }
            }
            
            LOGGER.log(Level.SEVERE, "Failed to load any Derby driver");
            return false;
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error checking Derby driver", e);
            return false;
        }
    }
    
    /**
     * Validates login form inputs
     * @param userEmail Email or username input
     * @param userPassword Password input
     * @return True if inputs are valid, false otherwise
     */
    private boolean validateLoginInputs(String userEmail, String userPassword) {
        // Check for empty fields
        if (userEmail.isEmpty() || userPassword.isEmpty()) {
            JOptionPane.showMessageDialog(
                this,
                "Please fill in all fields.",
                "Input Error",
                JOptionPane.WARNING_MESSAGE
            );
            return false;
        }
        
        // Check for excessive length (basic security measure)
        if (userEmail.length() > MAX_INPUT_LENGTH || userPassword.length() > MAX_INPUT_LENGTH) {
            JOptionPane.showMessageDialog(
                this,
                "Input is too long. Maximum length is " + MAX_INPUT_LENGTH + " characters.",
                "Input Error",
                JOptionPane.WARNING_MESSAGE
            );
            return false;
        }
        
        // If input looks like an email, validate its format
        if (userEmail.contains("@") && !EMAIL_PATTERN.matcher(userEmail).matches()) {
            JOptionPane.showMessageDialog(
                this,
                "Please enter a valid email address.",
                "Input Error",
                JOptionPane.WARNING_MESSAGE
            );
            return false;
        }
        
        return true;
    }
    
    /**
     * Checks user table status and returns user count
     * @param conn Database connection
     * @return Number of users in the database
     * @throws SQLException if a database error occurs
     */
    private int checkUserTableStatus(Connection conn) throws SQLException {
        int userCount = 0;
        try {
            PreparedStatement checkTable = conn.prepareStatement("SELECT COUNT(*) FROM Users");
            ResultSet countRs = checkTable.executeQuery();
            countRs.next();
            userCount = countRs.getInt(1);
            countRs.close();
            checkTable.close();

            // Also check APP.Users schema that might exist in some Derby DB configurations
            try {
                checkTable = conn.prepareStatement("SELECT COUNT(*) FROM APP.Users");
                countRs = checkTable.executeQuery();
                countRs.next();
                int appUserCount = countRs.getInt(1);
                
                if (appUserCount > userCount) {
                    userCount = appUserCount;
                }
                
                countRs.close();
                checkTable.close();
            } catch (SQLException e) {
                // APP.Users might not exist, which is fine
                LOGGER.log(Level.FINE, "APP.Users table not found, continuing with Users table", e);
            }
        } catch (SQLException e) {
            // Users table might not exist
            LOGGER.log(Level.WARNING, "Error checking Users table", e);
            throw new SQLException("Error checking user database: " + e.getMessage(), e);
        }
        
        return userCount;
    }
    
    /**
     * Attempts to authenticate a user with the provided credentials
     * @param conn Database connection
     * @param userInput Email or username input
     * @param hashedPassword Hashed password
     * @return True if authentication successful, false otherwise
     * @throws SQLException if a database error occurs
     */
    private boolean authenticateUser(Connection conn, String userInput, String hashedPassword) throws SQLException {
        boolean authenticated = false;
        
        // Try to authenticate with regular Users table
        authenticated = tryAuthenticate(conn, userInput, hashedPassword, "Users");
        
        // If that fails, try with APP.Users table
        if (!authenticated) {
            authenticated = tryAuthenticate(conn, userInput, hashedPassword, "APP.Users");
        }
        
        return authenticated;
    }
    
    /**
     * Attempts authentication against a specific database table
     * @param conn Database connection
     * @param userInput Email or username input
     * @param hashedPassword Hashed password
     * @param tableName Table name to query
     * @return True if authentication successful, false otherwise
     * @throws SQLException if a database error occurs
     */
    private boolean tryAuthenticate(Connection conn, String userInput, String hashedPassword, String tableName) throws SQLException {
        // First try email authentication
        String sql = "SELECT * FROM " + tableName + " WHERE Email = ? AND PasswordHash = ?";
        if (authenticateWithQuery(conn, sql, userInput, hashedPassword)) {
            return true;
        }
        
        // Then try username authentication
        sql = "SELECT * FROM " + tableName + " WHERE Username = ? AND PasswordHash = ?";
        return authenticateWithQuery(conn, sql, userInput, hashedPassword);
    }
    
    /**
     * Executes an authentication query
     * @param conn Database connection
     * @param sql SQL query to execute
     * @param userInput User input (email or username)
     * @param hashedPassword Hashed password
     * @return True if authentication successful, false otherwise
     * @throws SQLException if a database error occurs
     */
    private boolean authenticateWithQuery(Connection conn, String sql, String userInput, String hashedPassword) throws SQLException {
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, userInput);
            pstmt.setString(2, hashedPassword);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    int userId = rs.getInt("UserID");
                    String fullName = rs.getString("FullName");
                    String role = rs.getString("Role");
                    
                    // Store the current user ID
                    currentUserId = userId;
                    
                    try {
                        updateLastLoginDate(conn, userId);
                    } catch (SQLException e) {
                        LOGGER.log(Level.WARNING, "Failed to update last login date", e);
                        // Continue anyway as this is not critical
                    }
                    
                    LOGGER.log(Level.INFO, "User {0} logged in successfully", fullName);
                    
                    // Show success message dialog
                    JOptionPane.showMessageDialog(
                        this,
                        "Welcome, " + fullName + "!",
                        "Login Successful",
                        JOptionPane.INFORMATION_MESSAGE
                    );
                    
                    // Launch the appropriate dashboard based on role
                    launchDashboard(role);
                    
                    return true;
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Error during authentication query", e);
            throw e;
        }
        
        return false;
    }
    
    /**
     * Launch the appropriate dashboard based on user role
     * @param role User role
     */
    private void launchDashboard(String role) {
        if ("Librarian".equalsIgnoreCase(role)) {
            LOGGER.log(Level.INFO, "Launching Librarian Dashboard");
            SwingUtilities.invokeLater(() -> {
                DashboardLauncher.launchDashboard();
                this.dispose();
            });
        } else {
            LOGGER.log(Level.INFO, "Launching User Dashboard");
            SwingUtilities.invokeLater(() -> {
                DashboardLauncher.launchUserDashboard();
                this.dispose();
            });
        }
    }
    
    /**
     * Shows login failed message
     */
    private void showLoginFailedMessage() {
        LOGGER.log(Level.INFO, "Login failed: Invalid credentials");
        JOptionPane.showMessageDialog(
            this,
            "Invalid email/username or password. Please try again.",
            "Login Failed",
            JOptionPane.ERROR_MESSAGE
        );
    }
    
    /**
     * Handle SQL exceptions with appropriate user feedback
     * @param e The SQLException that occurred
     */
    private void handleSQLException(SQLException e) {
        LOGGER.log(Level.SEVERE, "SQL Error: " + e.getMessage() + 
                " (SQLState: " + e.getSQLState() + ", Error Code: " + e.getErrorCode() + ")", e);
        
        // Check for database connection issues
        if (e.getSQLState() != null &&
            (e.getSQLState().startsWith("XJ040") ||
             e.getSQLState().equals("XJ041") ||
             e.getSQLState().equals("40000") ||
             e.getSQLState().equals("XBM0A") ||
             e.getSQLState().equals("08001") ||
             e.getSQLState().equals("08003") ||  // Connection closed
             e.getSQLState().equals("08006"))) { // Database shutdown
            
            offerDatabaseRepair();
        } else if (e.getSQLState() != null && e.getSQLState().equals("42X05")) {
            // Table does not exist
            JOptionPane.showMessageDialog(
                this,
                "Database schema issue: Required tables don't exist. Please contact support.",
                "Database Error",
                JOptionPane.ERROR_MESSAGE
            );
        } else {
            JOptionPane.showMessageDialog(
                this,
                "A database error occurred: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE
            );
        }
    }

    private void updateLastLoginDate(Connection conn, int userId) throws SQLException {
        String sql = "UPDATE Users SET LastLoginDate = CURRENT_TIMESTAMP WHERE UserID = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setInt(1, userId);
            pstmt.executeUpdate();
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Failed to update last login date for user " + userId, e);
            throw e;
        }
    }

    private String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            LOGGER.log(Level.SEVERE, "Password hashing algorithm not available", e);
            JOptionPane.showMessageDialog(
                this,
                "Security error: Password hashing algorithm not available. Please contact support.",
                "Security Error",
                JOptionPane.ERROR_MESSAGE
            );
            // Return a placeholder that will never match instead of the plain password
            return "HASH_ALGORITHM_ERROR";
        }
    }

    private boolean createDefaultAdmin(Connection conn) {
        LOGGER.log(Level.INFO, "Creating default admin account");
        
        try {
            String adminPassword = "admin123";
            String adminHashedPassword = hashPassword(adminPassword);
            String sql = "INSERT INTO Users (Username, PasswordHash, PhoneNumber, Address, Email, FullName, Role, RegistrationDate) " +
                        "VALUES ('admin', ?, '************', 'Library Main Office', '<EMAIL>', 'Admin', 'Librarian', CURRENT_TIMESTAMP)";

            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, adminHashedPassword);
                pstmt.executeUpdate();
            }

            LOGGER.log(Level.INFO, "Default admin account created successfully");
            JOptionPane.showMessageDialog(
                this,
                "A default administrator account has been created.\nUsername: admin\nPassword: admin123\n\nPlease change this password after logging in.",
                "Default Account Created",
                JOptionPane.INFORMATION_MESSAGE
            );
            return true;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to create default admin account", e);
            JOptionPane.showMessageDialog(
                this,
                "Failed to create default admin account. Error: " + e.getMessage(),
                "Account Creation Failed",
                JOptionPane.ERROR_MESSAGE
            );
            return false;
        }
    }

    private void register_btnActionPerformed(java.awt.event.ActionEvent evt) {
        LOGGER.log(Level.INFO, "Opening registration form");
        try {
            Register register = new Register();
            register.setVisible(true);
            this.dispose();
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error opening registration form", e);
            JOptionPane.showMessageDialog(
                this,
                "Could not open registration form: " + e.getMessage(),
                "Error",
                JOptionPane.ERROR_MESSAGE
            );
        }
    }

    private void exit_btnActionPerformed(java.awt.event.ActionEvent evt) {
        // Shutdown the database properly before exiting
        DatabaseConnection.shutdownDatabase();

        // Exit the application
        System.exit(0);
    }

    private void offerDatabaseRepair() {
        LOGGER.log(Level.WARNING, "Database connection issues detected, offering repair");
        
        // Show a dialog to the user
        int choice = JOptionPane.showConfirmDialog(
            this,
            "The database connection is experiencing issues.\n\n" +
            "Would you like to attempt to repair the database?\n" +
            "This will:\n" +
            "1. Check for the Derby JDBC driver\n" +
            "2. Shut down existing connections\n" +
            "3. Remove corrupted database files if necessary\n" +
            "4. Restart the application\n\n" +
            "Note: Any unsaved data will be lost.",
            "Database Connection Issue",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        );

        if (choice == JOptionPane.YES_OPTION) {
            try {
                // Step 1: Check Derby driver
                JOptionPane.showMessageDialog(
                    this,
                    "Step 1/4: Checking Derby JDBC driver...",
                    "Database Repair",
                    JOptionPane.INFORMATION_MESSAGE
                );
                
                boolean driverLoaded = ensureDerbyDriverLoaded();
                if (!driverLoaded) {
                    JOptionPane.showMessageDialog(
                        this,
                        "Derby JDBC driver could not be found or loaded.\n" +
                        "Please ensure the Derby library is properly included in your project.",
                        "Driver Error",
                        JOptionPane.ERROR_MESSAGE
                    );
                    return;
                }
                
                // Step 2: Properly shut down the database
                JOptionPane.showMessageDialog(
                    this,
                    "Step 2/4: Shutting down database connections...",
                    "Database Repair",
                    JOptionPane.INFORMATION_MESSAGE
                );
                
                LOGGER.log(Level.INFO, "Shutting down database connections...");
                DatabaseConnection.shutdownDatabase();

                // Wait a moment to ensure connections are closed
                try {
                    Thread.sleep(1500);
                } catch (InterruptedException ie) {
                    LOGGER.log(Level.WARNING, "Interrupted while waiting for DB shutdown", ie);
                }
                
                // Step 3: Remove corrupted database files
                JOptionPane.showMessageDialog(
                    this,
                    "Step 3/4: Removing corrupted database files...",
                    "Database Repair",
                    JOptionPane.INFORMATION_MESSAGE
                );
                
                // Get database location
                String dbPath = System.getProperty("user.dir") + File.separator + "lms";
                File dbDir = new File(dbPath);
                
                if (dbDir.exists() && dbDir.isDirectory()) {
                    LOGGER.log(Level.INFO, "Found database directory: {0}", dbPath);
                    boolean deletionSuccess = deleteDirectory(dbDir);
                    
                    if (deletionSuccess) {
                        LOGGER.log(Level.INFO, "Successfully deleted corrupted database files");
                    } else {
                        LOGGER.log(Level.WARNING, "Failed to delete some database files");
                        JOptionPane.showMessageDialog(
                            this,
                            "Some database files could not be deleted. The application may still have problems.\n" +
                            "Please close all applications and try again.",
                            "Partial Cleanup",
                            JOptionPane.WARNING_MESSAGE
                        );
                    }
                } else {
                    LOGGER.log(Level.INFO, "Database directory does not exist: {0}", dbPath);
                }
                
                // Also check for derby.log and delete it
                File derbyLog = new File("derby.log");
                if (derbyLog.exists() && derbyLog.isFile()) {
                    if (derbyLog.delete()) {
                        LOGGER.log(Level.INFO, "Deleted derby.log file");
                    } else {
                        LOGGER.log(Level.WARNING, "Failed to delete derby.log file");
                    }
                }
                
                // Step 4: Restart the application
                JOptionPane.showMessageDialog(
                    this,
                    "Step 4/4: Database repair completed.\nThe application will now restart.",
                    "Database Repair",
                    JOptionPane.INFORMATION_MESSAGE
                );
                
                LOGGER.log(Level.INFO, "Restarting application...");
                restartApplication();
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "Failed during database repair", e);
                JOptionPane.showMessageDialog(
                    this,
                    "Failed to repair the database: " + e.getMessage() + "\n" +
                    "Please close and restart the application manually.",
                    "Repair Failed",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        } else {
            // User chose not to repair
            JOptionPane.showMessageDialog(
                this,
                "You may continue to use the application, but some features might not work correctly.\n" +
                "It's recommended to close and restart the application when convenient.",
                "Database Issue",
                JOptionPane.INFORMATION_MESSAGE
            );
        }
    }
    
    /**
     * Recursively delete a directory and all its contents
     * @param directory Directory to delete
     * @return true if successful, false otherwise
     */
    private boolean deleteDirectory(File directory) {
        if (!directory.exists()) {
            return true;
        }
        
        // Delete all files and subdirectories
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectory(file);
                } else {
                    if (!file.delete()) {
                        LOGGER.log(Level.WARNING, "Failed to delete file: {0}", file.getPath());
                        return false;
                    } else {
                        LOGGER.log(Level.FINE, "Deleted file: {0}", file.getPath());
                    }
                }
            }
        }
        
        // Delete the directory itself
        boolean result = directory.delete();
        if (!result) {
            LOGGER.log(Level.WARNING, "Failed to delete directory: {0}", directory.getPath());
        } else {
            LOGGER.log(Level.INFO, "Deleted directory: {0}", directory.getPath());
        }
        return result;
    }

    private void restartApplication() {
        try {
            // Get the command used to start this application
            String javaBin = System.getProperty("java.home") + File.separator + "bin" + File.separator + "java";
            String classpath = System.getProperty("java.class.path");
            String className = Login.class.getName();

            // Build the command to restart
            ProcessBuilder builder = new ProcessBuilder(javaBin, "-cp", classpath, className);
            builder.start();

            // Exit this instance
            System.exit(0);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Failed to restart application", e);
            JOptionPane.showMessageDialog(this,
                "Failed to restart application automatically. Please restart manually.",
                "Restart Failed", JOptionPane.WARNING_MESSAGE);
            System.exit(0);
        }
    }

    /**
     * Initialize database with schema and default data if needed
     * This should be called during application startup
     */
    private void initializeDatabase() {
        LOGGER.log(Level.INFO, "Initializing database...");
        
        try {
            // Register Derby driver
            try {
                Class.forName("org.apache.derby.jdbc.EmbeddedDriver").newInstance();
                LOGGER.log(Level.INFO, "Derby driver registered successfully");
            } catch (ClassNotFoundException | InstantiationException | IllegalAccessException e) {
                LOGGER.log(Level.SEVERE, "Failed to register Derby driver", e);
                JOptionPane.showMessageDialog(
                    this,
                    "Failed to initialize database driver. Error: " + e.getMessage(),
                    "Database Initialization Error",
                    JOptionPane.ERROR_MESSAGE
                );
                return;
            }
            
            // Get database location and check permissions
            String dbPath = System.getProperty("user.dir") + File.separator + "lms";
            File dbDir = new File(dbPath);
            File parentDir = dbDir.getParentFile();
            
            if (parentDir != null && !parentDir.exists()) {
                LOGGER.log(Level.WARNING, "Parent directory doesn't exist: {0}", parentDir.getPath());
                boolean created = parentDir.mkdirs();
                if (!created) {
                    LOGGER.log(Level.SEVERE, "Failed to create parent directory");
                }
            }
            
            // Try to establish connection and create tables
            try (Connection conn = java.sql.DriverManager.getConnection("**************************")) {
                LOGGER.log(Level.INFO, "Database connection established for initialization");
                
                // Create Users table
                try {
                    String createUsersTable = 
                        "CREATE TABLE Users (" +
                        "UserID INT NOT NULL GENERATED ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1), " +
                        "Username VARCHAR(50) NOT NULL UNIQUE, " +
                        "PasswordHash VARCHAR(256) NOT NULL, " +
                        "PhoneNumber VARCHAR(20), " +
                        "Address VARCHAR(100), " +
                        "Email VARCHAR(100) UNIQUE, " +
                        "FullName VARCHAR(100) NOT NULL, " +
                        "Role VARCHAR(20) NOT NULL, " +
                        "RegistrationDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                        "LastLoginDate TIMESTAMP, " +
                        "PRIMARY KEY (UserID)" +
                        ")";
                    
                    try (PreparedStatement pstmt = conn.prepareStatement(createUsersTable)) {
                        pstmt.executeUpdate();
                        LOGGER.log(Level.INFO, "Created Users table successfully");
                    }
                } catch (SQLException e) {
                    // Table might already exist
                    if (e.getSQLState() != null && e.getSQLState().equals("X0Y32")) {
                        LOGGER.log(Level.INFO, "Users table already exists");
                    } else {
                        throw e;
                    }
                }
                
                // Add more schema initialization as needed
                
            } catch (SQLException e) {
                LOGGER.log(Level.SEVERE, "Error initializing database: {0} (SQLState: {1})", 
                           new Object[]{e.getMessage(), e.getSQLState()});
            }
            
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Unexpected error during database initialization", e);
        }
    }

    public static void main(String args[]) {
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(Login.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(Login.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(Login.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(Login.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new Login().setVisible(true);
            }
        });
    }

    private javax.swing.JTextField email;
    private javax.swing.JButton exit_btn;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JPanel jPanel2;
    private javax.swing.JButton login_btn;
    private javax.swing.JPasswordField password;
    private javax.swing.JButton register_btn;
    private javax.swing.JCheckBox show_pass;
}
