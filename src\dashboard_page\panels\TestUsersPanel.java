package dashboard_page.panels;

import javax.swing.*;
import java.awt.*;

public class TestUsersPanel {
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("Creating UsersContentPanel...");
                UsersContentPanel panel = new UsersContentPanel();
                System.out.println("UsersContentPanel created successfully");
                
                JFrame frame = new JFrame("Test Users Panel");
                frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
                frame.setSize(800, 600);
                frame.setLayout(new BorderLayout());
                frame.add(panel, BorderLayout.CENTER);
                frame.setLocationRelativeTo(null);
                frame.setVisible(true);
                
                System.out.println("Test frame displayed");
            } catch (Exception e) {
                System.err.println("Error creating UsersContentPanel: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
}
