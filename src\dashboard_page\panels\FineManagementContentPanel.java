package dashboard_page.panels;

import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import library.management.system.database.DatabaseConnection;

public class FineManagementContentPanel extends JPanel {

    // Static instance for access from other panels
    private static FineManagementContentPanel instance;

    private JTable finesTable;
    private DefaultTableModel tableModel;
    private JButton refreshButton;
    private JButton addFineButton;
    private JButton deleteFineButton;
    private JTextField searchField;
    private JComboBox<String> statusFilterComboBox;
    private JLabel totalFinesLabel;
    private double totalUnpaidFines = 0.0;

    // Map to store loan IDs and their due dates
    private Map<Integer, Date> loanDueDates = new HashMap<>();

    // Status options for fines
    private static final String[] FINE_STATUSES = {
        "All", "Paid", "Unpaid"
    };

    /**
     * Static method to refresh the fines data from other panels
     */
    public static void refreshData() {
        if (instance != null) {
            instance.loadFinesData();
        }
    }

    public FineManagementContentPanel() {
        initComponents();
        loadFinesData();
        instance = this;
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));
        setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // Create top panel with search and filter
        JPanel topPanel = new JPanel(new BorderLayout());

        // Create filter panel
        JPanel filterPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        filterPanel.add(new JLabel("Status:"));
        statusFilterComboBox = new JComboBox<>(FINE_STATUSES);
        statusFilterComboBox.setPreferredSize(new Dimension(120, 25));
        statusFilterComboBox.addActionListener(e -> loadFinesData());
        filterPanel.add(statusFilterComboBox);

        // Create search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        searchField = new JTextField(20);
        searchField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyPressed(java.awt.event.KeyEvent e) {
                if (e.getKeyCode() == java.awt.event.KeyEvent.VK_ENTER) {
                    searchFines();
                }
            }
        });

        JButton searchButton = new JButton("Search");
        searchButton.setFont(new Font("Arial", Font.BOLD, 12));
        searchButton.setBackground(new Color(70, 130, 180));
        searchButton.setForeground(Color.WHITE);
        searchButton.addActionListener(e -> searchFines());

        JButton clearButton = new JButton("Clear");
        clearButton.setFont(new Font("Arial", Font.BOLD, 12));
        clearButton.setBackground(new Color(180, 180, 180));
        clearButton.addActionListener(e -> {
            searchField.setText("");
            loadFinesData();
        });

        searchPanel.add(new JLabel("Search:"));
        searchPanel.add(searchField);
        searchPanel.add(searchButton);
        searchPanel.add(clearButton);

        topPanel.add(filterPanel, BorderLayout.WEST);
        topPanel.add(searchPanel, BorderLayout.EAST);

        add(topPanel, BorderLayout.NORTH);

        // Create table
        String[] columns = {"Fine ID", "User", "Book Title", "Fine Date", "Due Date", "Amount", "Reason", "Status"};
        tableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        finesTable = new JTable(tableModel);
        finesTable.setFillsViewportHeight(true);
        finesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        finesTable.setRowHeight(35);
        finesTable.setShowGrid(true);
        finesTable.setGridColor(new Color(200, 200, 200));
        finesTable.setFont(new Font("Arial", Font.PLAIN, 12));

        JTableHeader header = finesTable.getTableHeader();
        header.setFont(new Font("Arial", Font.BOLD, 14));
        header.setBackground(new Color(220, 220, 220));
        header.setForeground(new Color(50, 50, 50));
        header.setPreferredSize(new Dimension(header.getWidth(), 40));

        ((DefaultTableCellRenderer)header.getDefaultRenderer()).setHorizontalAlignment(JLabel.CENTER);

        // Set column widths
        finesTable.getColumnModel().getColumn(0).setPreferredWidth(60);  // Fine ID
        finesTable.getColumnModel().getColumn(1).setPreferredWidth(150); // User
        finesTable.getColumnModel().getColumn(2).setPreferredWidth(200); // Book Title
        finesTable.getColumnModel().getColumn(3).setPreferredWidth(100); // Fine Date
        finesTable.getColumnModel().getColumn(4).setPreferredWidth(100); // Due Date
        finesTable.getColumnModel().getColumn(5).setPreferredWidth(80);  // Amount
        finesTable.getColumnModel().getColumn(6).setPreferredWidth(200); // Reason
        finesTable.getColumnModel().getColumn(7).setPreferredWidth(80);  // Status

        // Custom renderer for cells
        finesTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (!isSelected) {
                    c.setBackground(row % 2 == 0 ? new Color(245, 245, 245) : Color.WHITE);
                    c.setForeground(new Color(50, 50, 50));
                } else {
                    c.setBackground(new Color(230, 230, 250));
                    c.setForeground(new Color(50, 50, 50));
                }

                if (c instanceof JLabel) {
                    JLabel label = (JLabel) c;
                    label.setBorder(BorderFactory.createEmptyBorder(0, 8, 0, 8));

                    // Center align numeric columns and status
                    if (column == 0 || column == 3 || column == 4 || column == 5 || column == 7) {
                        label.setHorizontalAlignment(JLabel.CENTER);

                        // Format amount column
                        if (column == 5 && value != null) {
                            if (value instanceof Double) {
                                label.setText(String.format("₱%.2f", (Double)value));
                            } else if (value instanceof String && !((String)value).startsWith("₱")) {
                                try {
                                    double amount = Double.parseDouble((String)value);
                                    label.setText(String.format("₱%.2f", amount));
                                } catch (NumberFormatException e) {
                                    // Keep original text if not a valid number
                                }
                            }
                        }

                        // Color status column
                        if (column == 7 && value != null) {
                            String status = value.toString();
                            if ("Paid".equals(status)) {
                                label.setForeground(new Color(0, 128, 0)); // Green
                            } else if ("Unpaid".equals(status)) {
                                label.setForeground(new Color(220, 0, 0)); // Red
                            }
                        }

                        // Highlight overdue dates
                        if (column == 4 && value != null) {
                            try {
                                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                Date dueDate = dateFormat.parse(value.toString());
                                Date today = new Date();
                                if (dueDate.before(today)) {
                                    label.setForeground(new Color(220, 0, 0)); // Red for overdue
                                }
                            } catch (Exception e) {
                                // Ignore parsing errors
                            }
                        }
                    } else {
                        label.setHorizontalAlignment(JLabel.LEFT);
                    }
                }

                return c;
            }
        });

        // Add selection listener
        finesTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int selectedRow = finesTable.getSelectedRow();
                if (selectedRow >= 0) {
                    String status = (String) finesTable.getValueAt(selectedRow, 7);
                    Object fineId = finesTable.getValueAt(selectedRow, 0);

                    // Only enable delete button for actual fines (not overdue books without fines)
                    deleteFineButton.setEnabled(fineId != null && !"No Fine".equals(status));

                    // If this is an overdue book without a fine, change the "Add Fine" button text
                    if ("No Fine".equals(status)) {
                        addFineButton.setText("Create Fine");
                    } else {
                        addFineButton.setText("Add Fine");
                    }
                } else {
                    deleteFineButton.setEnabled(false);
                    addFineButton.setText("Add Fine");
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(finesTable);
        add(scrollPane, BorderLayout.CENTER);

        // Create summary panel
        JPanel summaryPanel = new JPanel(new BorderLayout());
        summaryPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0));

        totalFinesLabel = new JLabel("Total Unpaid Fines: ₱0.00");
        totalFinesLabel.setFont(new Font("Arial", Font.BOLD, 14));
        totalFinesLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 0));

        summaryPanel.add(totalFinesLabel, BorderLayout.WEST);

        // Create button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        buttonPanel.setBackground(new Color(240, 240, 240));

        refreshButton = new JButton("Refresh");
        refreshButton.setFont(new Font("Arial", Font.BOLD, 14));
        refreshButton.setBackground(new Color(100, 181, 246));
        refreshButton.setForeground(Color.WHITE);
        refreshButton.setFocusPainted(false);
        refreshButton.setPreferredSize(new Dimension(120, 40));
        refreshButton.addActionListener(e -> loadFinesData());

        addFineButton = new JButton("Add Fine");
        addFineButton.setFont(new Font("Arial", Font.BOLD, 14));
        addFineButton.setBackground(new Color(46, 125, 50));
        addFineButton.setForeground(Color.WHITE);
        addFineButton.setFocusPainted(false);
        addFineButton.setPreferredSize(new Dimension(120, 40));
        addFineButton.addActionListener(e -> showAddFineDialog());



        deleteFineButton = new JButton("Delete Fine");
        deleteFineButton.setFont(new Font("Arial", Font.BOLD, 14));
        deleteFineButton.setBackground(new Color(220, 53, 69)); // Red color
        deleteFineButton.setForeground(Color.WHITE);
        deleteFineButton.setFocusPainted(false);
        deleteFineButton.setPreferredSize(new Dimension(120, 40));
        deleteFineButton.setEnabled(false);
        deleteFineButton.addActionListener(e -> deleteFine());

        buttonPanel.add(refreshButton);
        buttonPanel.add(addFineButton);
        buttonPanel.add(deleteFineButton);

        summaryPanel.add(buttonPanel, BorderLayout.EAST);
        add(summaryPanel, BorderLayout.SOUTH);
    }

    /**
     * Load all fines data and overdue loans from the database
     */
    public void loadFinesData() {
        try {
            tableModel.setRowCount(0);
            totalUnpaidFines = 0.0;

            // Get the selected status filter
            String statusFilter = (String) statusFilterComboBox.getSelectedItem();

            Connection conn = DatabaseConnection.getConnection();

            // First, get all existing fines
            String finesQuery = "SELECT f.FineID, u.FullName, b.Title, f.FineDate, l.DueDate, f.Amount as FineAmount, " +
                          "f.Reason, f.Status as PaymentStatus, l.LoanID " +
                          "FROM Fines f " +
                          "JOIN Users u ON f.UserID = u.UserID " +
                          "JOIN Loans l ON f.LoanID = l.LoanID " +
                          "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                          "JOIN Books b ON bc.BookID = b.BookID " +
                          "WHERE 1=1 ";

            // Add status filter if not "All"
            if (!"All".equals(statusFilter)) {
                finesQuery += "AND f.Status = ? ";
            }

            finesQuery += "ORDER BY f.FineDate DESC";

            PreparedStatement finesStmt = conn.prepareStatement(finesQuery);

            // Set status parameter if needed
            if (!"All".equals(statusFilter)) {
                finesStmt.setString(1, statusFilter);
            }

            ResultSet rs = finesStmt.executeQuery();

            // Set to track loan IDs that already have fines
            Set<Integer> loansWithFines = new HashSet<>();

            while (rs.next()) {
                int fineId = rs.getInt("FineID");
                String userName = rs.getString("FullName");
                String bookTitle = rs.getString("Title");
                Date fineDate = rs.getDate("FineDate");
                Date dueDate = rs.getDate("DueDate");
                double amount = rs.getDouble("FineAmount");
                String reason = rs.getString("Reason");
                String status = rs.getString("PaymentStatus");
                int loanId = rs.getInt("LoanID");

                // Add to the set of loans that already have fines
                loansWithFines.add(loanId);

                // Format the dates
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String formattedFineDate = fineDate != null ? dateFormat.format(fineDate) : "";
                String formattedDueDate = dueDate != null ? dateFormat.format(dueDate) : "";

                tableModel.addRow(new Object[]{
                    fineId,
                    userName,
                    bookTitle,
                    formattedFineDate,
                    formattedDueDate,
                    amount,
                    reason,
                    status
                });

                // Calculate total unpaid fines
                if ("Unpaid".equals(status)) {
                    totalUnpaidFines += amount;
                }
            }

            rs.close();
            finesStmt.close();

            // Now, get all overdue loans that don't have fines yet
            String overdueQuery = "SELECT l.LoanID, u.FullName, b.Title, l.DueDate " +
                                 "FROM Loans l " +
                                 "JOIN Users u ON l.UserID = u.UserID " +
                                 "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                                 "JOIN Books b ON bc.BookID = b.BookID " +
                                 "WHERE l.ReturnDate IS NULL " +
                                 "AND l.DueDate < CURRENT_DATE " +
                                 "AND l.Status = 'Approved' " +
                                 "ORDER BY l.DueDate ASC";

            PreparedStatement overdueStmt = conn.prepareStatement(overdueQuery);
            ResultSet overdueRs = overdueStmt.executeQuery();

            while (overdueRs.next()) {
                int loanId = overdueRs.getInt("LoanID");

                // Skip if this loan already has a fine
                if (loansWithFines.contains(loanId)) {
                    continue;
                }

                String userName = overdueRs.getString("FullName");
                String bookTitle = overdueRs.getString("Title");
                Date dueDate = overdueRs.getDate("DueDate");

                // Format the due date
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String formattedDueDate = dueDate != null ? dateFormat.format(dueDate) : "";

                // Calculate days overdue
                long daysOverdue = 0;
                if (dueDate != null) {
                    long diff = System.currentTimeMillis() - dueDate.getTime();
                    daysOverdue = diff / (24 * 60 * 60 * 1000);
                }

                // Add to table with null fine ID to indicate it's just an overdue book without a fine
                tableModel.addRow(new Object[]{
                    null, // No fine ID
                    userName,
                    bookTitle,
                    "", // No fine date
                    formattedDueDate,
                    0.0, // No amount
                    "Overdue by " + daysOverdue + " days", // Reason shows days overdue
                    "No Fine" // Special status
                });
            }

            overdueRs.close();
            overdueStmt.close();

            // Update total fines label
            totalFinesLabel.setText(String.format("Total Unpaid Fines: ₱%.2f", totalUnpaidFines));

            // No need to disable buttons here

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error loading fines data: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    // Sample fines generation has been removed to allow manual creation of fines

    /**
     * Search for fines based on the search term
     */
    private void searchFines() {
        String searchTerm = searchField.getText().trim();
        if (searchTerm.isEmpty()) {
            loadFinesData();
            return;
        }

        try {
            tableModel.setRowCount(0);
            totalUnpaidFines = 0.0;

            // Get the selected status filter
            String statusFilter = (String) statusFilterComboBox.getSelectedItem();

            Connection conn = DatabaseConnection.getConnection();

            // Build the query based on the search term and status filter
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("SELECT f.FineID, u.FullName, b.Title, f.FineDate, l.DueDate, f.Amount as FineAmount, ")
                       .append("f.Reason, f.Status as PaymentStatus, l.LoanID ")
                       .append("FROM Fines f ")
                       .append("JOIN Users u ON f.UserID = u.UserID ")
                       .append("JOIN Loans l ON f.LoanID = l.LoanID ")
                       .append("JOIN BookCopies bc ON l.CopyID = bc.CopyID ")
                       .append("JOIN Books b ON bc.BookID = b.BookID ")
                       .append("WHERE 1=1 ")
                       .append("AND (LOWER(u.FullName) LIKE ? OR LOWER(b.Title) LIKE ? OR LOWER(f.Reason) LIKE ?) ");

            // Add status filter if not "All"
            if (!"All".equals(statusFilter)) {
                queryBuilder.append("AND f.Status = ? ");
            }

            queryBuilder.append("ORDER BY f.FineDate DESC");

            PreparedStatement stmt = conn.prepareStatement(queryBuilder.toString());

            // Set search parameters
            String searchPattern = "%" + searchTerm.toLowerCase() + "%";
            stmt.setString(1, searchPattern);
            stmt.setString(2, searchPattern);
            stmt.setString(3, searchPattern);

            // Set status parameter if needed
            if (!"All".equals(statusFilter)) {
                stmt.setString(4, statusFilter);
            }

            ResultSet rs = stmt.executeQuery();

            // Set to track loan IDs that already have fines
            Set<Integer> loansWithFines = new HashSet<>();

            while (rs.next()) {
                int fineId = rs.getInt("FineID");
                String userName = rs.getString("FullName");
                String bookTitle = rs.getString("Title");
                Date fineDate = rs.getDate("FineDate");
                Date dueDate = rs.getDate("DueDate");
                double amount = rs.getDouble("FineAmount");
                String reason = rs.getString("Reason");
                String status = rs.getString("PaymentStatus");
                int loanId = rs.getInt("LoanID");

                // Add to the set of loans that already have fines
                loansWithFines.add(loanId);

                // Format the dates
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String formattedFineDate = fineDate != null ? dateFormat.format(fineDate) : "";
                String formattedDueDate = dueDate != null ? dateFormat.format(dueDate) : "";

                tableModel.addRow(new Object[]{
                    fineId,
                    userName,
                    bookTitle,
                    formattedFineDate,
                    formattedDueDate,
                    amount,
                    reason,
                    status
                });

                // Calculate total unpaid fines
                if ("Unpaid".equals(status)) {
                    totalUnpaidFines += amount;
                }
            }

            rs.close();
            stmt.close();

            // Now, get all overdue loans that match the search term and don't have fines yet
            String overdueQuery = "SELECT l.LoanID, u.FullName, b.Title, l.DueDate " +
                                 "FROM Loans l " +
                                 "JOIN Users u ON l.UserID = u.UserID " +
                                 "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                                 "JOIN Books b ON bc.BookID = b.BookID " +
                                 "WHERE l.ReturnDate IS NULL " +
                                 "AND l.DueDate < CURRENT_DATE " +
                                 "AND l.Status = 'Approved' " +
                                 "AND (LOWER(u.FullName) LIKE ? OR LOWER(b.Title) LIKE ?) " +
                                 "ORDER BY l.DueDate ASC";

            PreparedStatement overdueStmt = conn.prepareStatement(overdueQuery);

            // Set search parameters
            overdueStmt.setString(1, searchPattern);
            overdueStmt.setString(2, searchPattern);

            ResultSet overdueRs = overdueStmt.executeQuery();

            while (overdueRs.next()) {
                int loanId = overdueRs.getInt("LoanID");

                // Skip if this loan already has a fine
                if (loansWithFines.contains(loanId)) {
                    continue;
                }

                String userName = overdueRs.getString("FullName");
                String bookTitle = overdueRs.getString("Title");
                Date dueDate = overdueRs.getDate("DueDate");

                // Format the due date
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String formattedDueDate = dueDate != null ? dateFormat.format(dueDate) : "";

                // Calculate days overdue
                long daysOverdue = 0;
                if (dueDate != null) {
                    long diff = System.currentTimeMillis() - dueDate.getTime();
                    daysOverdue = diff / (24 * 60 * 60 * 1000);
                }

                // Add to table with null fine ID to indicate it's just an overdue book without a fine
                tableModel.addRow(new Object[]{
                    null, // No fine ID
                    userName,
                    bookTitle,
                    "", // No fine date
                    formattedDueDate,
                    0.0, // No amount
                    "Overdue by " + daysOverdue + " days", // Reason shows days overdue
                    "No Fine" // Special status
                });
            }

            overdueRs.close();
            overdueStmt.close();

            // Update total fines label
            totalFinesLabel.setText(String.format("Total Unpaid Fines: ₱%.2f", totalUnpaidFines));

            // No need to disable buttons here

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error searching fines: " + e.getMessage(),
                "Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Show dialog to add a new fine
     */
    private void showAddFineDialog() {
        // Check if we're creating a fine for a selected overdue book
        final boolean[] isUpdatingRow = {false};
        final int[] selectedRow = {-1};
        final int[] selectedLoanId = {-1};

        // If a row is selected and it has "No Fine" status, we'll update it
        if (finesTable.getSelectedRow() >= 0) {
            selectedRow[0] = finesTable.getSelectedRow();
            String status = (String) finesTable.getValueAt(selectedRow[0], 7);

            if ("No Fine".equals(status)) {
                isUpdatingRow[0] = true;
            }
        }

        String dialogTitle = isUpdatingRow[0] ? "Create Fine for Overdue Book" : "Add New Fine";
        JDialog addFineDialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), dialogTitle, true);
        addFineDialog.setSize(500, 400);
        addFineDialog.setLocationRelativeTo(this);
        addFineDialog.setLayout(new BorderLayout());

        JPanel formPanel = new JPanel(new GridBagLayout());
        formPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.insets = new Insets(5, 5, 5, 5);

        // Create a map to store user IDs
        Map<String, Integer> userMap = new HashMap<>();
        // Create a map to store loan IDs and associated book titles
        Map<String, Integer> loanMap = new HashMap<>();

        try {
            Connection conn = DatabaseConnection.getConnection();

            // Get only users with 'User' role (exclude librarians)
            Statement userStmt = conn.createStatement();
            ResultSet userRs = userStmt.executeQuery("SELECT UserID, FullName FROM Users WHERE Role = 'User' ORDER BY FullName");

            while (userRs.next()) {
                int userId = userRs.getInt("UserID");
                String fullName = userRs.getString("FullName");
                userMap.put(fullName, userId);
            }

            userRs.close();
            userStmt.close();

            // Get overdue loans with book titles and due dates
            Statement loanStmt = conn.createStatement();
            ResultSet loanRs = loanStmt.executeQuery(
                "SELECT l.LoanID, b.Title, u.FullName, l.DueDate " +
                "FROM Loans l " +
                "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                "JOIN Books b ON bc.BookID = b.BookID " +
                "JOIN Users u ON l.UserID = u.UserID " +
                "WHERE l.ReturnDate IS NULL AND l.DueDate < CURRENT_DATE " +
                "ORDER BY b.Title");

            // Clear the existing loan due dates map
            loanDueDates.clear();

            while (loanRs.next()) {
                int loanId = loanRs.getInt("LoanID");
                String title = loanRs.getString("Title");
                String userName = loanRs.getString("FullName");
                Date dueDate = loanRs.getDate("DueDate");

                // Store the loan ID and due date
                loanDueDates.put(loanId, dueDate);

                loanMap.put(title + " (Borrowed by: " + userName + ")", loanId);
            }

            loanRs.close();
            loanStmt.close();

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(addFineDialog,
                "Error loading users and loans: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }

        // User selection
        gbc.gridx = 0;
        gbc.gridy = 0;
        formPanel.add(new JLabel("User:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> userComboBox = new JComboBox<>(userMap.keySet().toArray(new String[0]));

        // If updating an existing row, pre-select the user
        if (isUpdatingRow[0]) {
            String selectedUserName = (String) finesTable.getValueAt(selectedRow[0], 1);
            for (int i = 0; i < userComboBox.getItemCount(); i++) {
                if (userComboBox.getItemAt(i).equals(selectedUserName)) {
                    userComboBox.setSelectedIndex(i);
                    break;
                }
            }
            // Disable user selection when updating an existing row
            userComboBox.setEnabled(!isUpdatingRow[0]);
        }

        formPanel.add(userComboBox, gbc);

        // Loan/Book selection
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.weightx = 0.0;
        formPanel.add(new JLabel("Book Loan:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> loanComboBox = new JComboBox<>(loanMap.keySet().toArray(new String[0]));

        // If updating an existing row, pre-select the book
        if (isUpdatingRow[0]) {
            String selectedBookTitle = (String) finesTable.getValueAt(selectedRow[0], 2);
            for (int i = 0; i < loanComboBox.getItemCount(); i++) {
                String item = loanComboBox.getItemAt(i);
                if (item.startsWith(selectedBookTitle + " (Borrowed by:")) {
                    loanComboBox.setSelectedIndex(i);
                    // Store the loan ID for later use
                    selectedLoanId[0] = loanMap.get(item);
                    break;
                }
            }
            // Disable loan selection when updating an existing row
            loanComboBox.setEnabled(!isUpdatingRow[0]);
        }

        formPanel.add(loanComboBox, gbc);

        // Amount
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.weightx = 0.0;
        formPanel.add(new JLabel("Amount (₱):"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField amountField = new JTextField();
        formPanel.add(amountField, gbc);

        // Reason
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.weightx = 0.0;
        formPanel.add(new JLabel("Reason:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> reasonComboBox = new JComboBox<>(new String[]{"Late return", "Damaged book", "Lost book", "Other"});
        formPanel.add(reasonComboBox, gbc);

        // Custom reason (for "Other")
        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.weightx = 0.0;
        formPanel.add(new JLabel("Custom Reason:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField customReasonField = new JTextField();
        customReasonField.setEnabled(false);
        formPanel.add(customReasonField, gbc);

        // Enable custom reason field when "Other" is selected
        reasonComboBox.addActionListener(e -> {
            customReasonField.setEnabled("Other".equals(reasonComboBox.getSelectedItem()));
        });

        // Buttons
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton saveButton = new JButton("Save");
        JButton cancelButton = new JButton("Cancel");

        saveButton.addActionListener(e -> {
            // Validate input
            if (userComboBox.getSelectedItem() == null) {
                JOptionPane.showMessageDialog(addFineDialog,
                    "Please select a user.",
                    "Validation Error",
                    JOptionPane.ERROR_MESSAGE);
                return;
            }

            if (loanComboBox.getSelectedItem() == null) {
                JOptionPane.showMessageDialog(addFineDialog,
                    "Please select a book loan.",
                    "Validation Error",
                    JOptionPane.ERROR_MESSAGE);
                return;
            }

            if (amountField.getText().trim().isEmpty()) {
                JOptionPane.showMessageDialog(addFineDialog,
                    "Please enter an amount.",
                    "Validation Error",
                    JOptionPane.ERROR_MESSAGE);
                return;
            }

            try {
                double amount = Double.parseDouble(amountField.getText().trim());
                if (amount <= 0) {
                    JOptionPane.showMessageDialog(addFineDialog,
                        "Amount must be greater than zero.",
                        "Validation Error",
                        JOptionPane.ERROR_MESSAGE);
                    return;
                }

                // Get reason
                String reason = reasonComboBox.getSelectedItem().toString();
                if ("Other".equals(reason)) {
                    if (customReasonField.getText().trim().isEmpty()) {
                        JOptionPane.showMessageDialog(addFineDialog,
                            "Please enter a custom reason.",
                            "Validation Error",
                            JOptionPane.ERROR_MESSAGE);
                        return;
                    }
                    reason = customReasonField.getText().trim();
                }

                // Get selected user and loan
                String selectedUser = userComboBox.getSelectedItem().toString();
                String selectedLoan = loanComboBox.getSelectedItem().toString();

                int userId = userMap.get(selectedUser);
                int loanId = loanMap.get(selectedLoan);

                // Save to database
                try {
                    Connection conn = DatabaseConnection.getConnection();

                    // Get the book title from the selected loan
                    String bookTitle = selectedLoan.split(" \\(Borrowed by:")[0];

                    // Format dates
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date today = new Date();

                    // Get the due date for this loan
                    Date dueDate = loanDueDates.get(loanId);
                    String formattedDueDate = dueDate != null ? dateFormat.format(dueDate) : "";

                    // Check if we're updating an existing row or creating a new fine
                    if (isUpdatingRow[0] && selectedRow[0] >= 0) {
                        // We're updating an existing row (creating a fine for an overdue book)
                        String insertSql = "INSERT INTO Fines (LoanID, UserID, Amount, FineDate, Reason, Status) " +
                                          "VALUES (?, ?, ?, CURRENT_DATE, ?, 'Unpaid')";

                        PreparedStatement pstmt = conn.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS);
                        pstmt.setInt(1, loanId);
                        pstmt.setInt(2, userId);
                        pstmt.setDouble(3, amount);
                        pstmt.setString(4, reason);

                        int rowsAffected = pstmt.executeUpdate();

                        if (rowsAffected > 0) {
                            // Get the generated fine ID
                            ResultSet generatedKeys = pstmt.getGeneratedKeys();
                            int newFineId = 0;
                            if (generatedKeys.next()) {
                                newFineId = generatedKeys.getInt(1);
                            }
                            generatedKeys.close();

                            // Update the existing row instead of adding a new one
                            tableModel.setValueAt(newFineId, selectedRow[0], 0);
                            tableModel.setValueAt(dateFormat.format(today), selectedRow[0], 3);
                            tableModel.setValueAt(amount, selectedRow[0], 5);
                            tableModel.setValueAt(reason, selectedRow[0], 6);
                            tableModel.setValueAt("Unpaid", selectedRow[0], 7);

                            // Update total unpaid fines
                            totalUnpaidFines += amount;
                            totalFinesLabel.setText(String.format("Total Unpaid Fines: ₱%.2f", totalUnpaidFines));

                            JOptionPane.showMessageDialog(addFineDialog,
                                "Fine created successfully.",
                                "Success",
                                JOptionPane.INFORMATION_MESSAGE);

                            addFineDialog.dispose();
                        } else {
                            JOptionPane.showMessageDialog(addFineDialog,
                                "Failed to create fine in database.",
                                "Error",
                                JOptionPane.ERROR_MESSAGE);
                        }

                        pstmt.close();
                    } else {
                        // We're adding a completely new fine
                        String insertSql = "INSERT INTO Fines (LoanID, UserID, Amount, FineDate, Reason, Status) " +
                                          "VALUES (?, ?, ?, CURRENT_DATE, ?, 'Unpaid')";

                        PreparedStatement pstmt = conn.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS);
                        pstmt.setInt(1, loanId);
                        pstmt.setInt(2, userId);
                        pstmt.setDouble(3, amount);
                        pstmt.setString(4, reason);

                        int rowsAffected = pstmt.executeUpdate();

                        if (rowsAffected > 0) {
                            // Get the generated fine ID
                            ResultSet generatedKeys = pstmt.getGeneratedKeys();
                            int newFineId = 0;
                            if (generatedKeys.next()) {
                                newFineId = generatedKeys.getInt(1);
                            }
                            generatedKeys.close();

                            // Add a new row to the table
                            tableModel.addRow(new Object[]{
                                newFineId,
                                selectedUser,
                                bookTitle,
                                dateFormat.format(today),
                                formattedDueDate,
                                amount,
                                reason,
                                "Unpaid"
                            });

                            // Update total unpaid fines
                            totalUnpaidFines += amount;
                            totalFinesLabel.setText(String.format("Total Unpaid Fines: ₱%.2f", totalUnpaidFines));

                            JOptionPane.showMessageDialog(addFineDialog,
                                "Fine added successfully.",
                                "Success",
                                JOptionPane.INFORMATION_MESSAGE);

                            addFineDialog.dispose();
                        } else {
                            JOptionPane.showMessageDialog(addFineDialog,
                                "Failed to add fine to database.",
                                "Error",
                                JOptionPane.ERROR_MESSAGE);
                        }

                        pstmt.close();
                    }

                    DatabaseConnection.closeConnection(conn);

                } catch (Exception ex) {
                    ex.printStackTrace();
                    JOptionPane.showMessageDialog(addFineDialog,
                        "Error adding fine to database: " + ex.getMessage(),
                        "Database Error",
                        JOptionPane.ERROR_MESSAGE);
                }

            } catch (NumberFormatException ex) {
                JOptionPane.showMessageDialog(addFineDialog,
                    "Please enter a valid amount.",
                    "Validation Error",
                    JOptionPane.ERROR_MESSAGE);
            }
        });

        cancelButton.addActionListener(e -> addFineDialog.dispose());

        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);

        addFineDialog.add(formPanel, BorderLayout.CENTER);
        addFineDialog.add(buttonPanel, BorderLayout.SOUTH);
        addFineDialog.setVisible(true);
    }



    /**
     * Delete the selected fine
     */
    private void deleteFine() {
        int selectedRow = finesTable.getSelectedRow();
        if (selectedRow >= 0) {
            // Convert view row index to model row index in case table is sorted
            int modelRow = finesTable.convertRowIndexToModel(selectedRow);

            int fineId = Integer.parseInt(tableModel.getValueAt(modelRow, 0).toString());
            String user = tableModel.getValueAt(modelRow, 1).toString();
            String bookTitle = tableModel.getValueAt(modelRow, 2).toString();
            double amount = (double) tableModel.getValueAt(modelRow, 5);
            String status = (String) tableModel.getValueAt(modelRow, 7);

            int confirm = JOptionPane.showConfirmDialog(this,
                "Are you sure you want to delete the fine of ₱" + String.format("%.2f", amount) +
                " for " + user + " for book '" + bookTitle + "'?",
                "Confirm Deletion",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

            if (confirm == JOptionPane.YES_OPTION) {
                try {
                    Connection conn = DatabaseConnection.getConnection();

                    // Delete the fine from the database
                    String deleteSql = "DELETE FROM Fines WHERE FineID = ?";
                    PreparedStatement pstmt = conn.prepareStatement(deleteSql);
                    pstmt.setInt(1, fineId);

                    int rowsAffected = pstmt.executeUpdate();
                    pstmt.close();

                    if (rowsAffected > 0) {
                        // If the fine was unpaid, update the total
                        if ("Unpaid".equals(status)) {
                            totalUnpaidFines -= amount;
                            totalFinesLabel.setText(String.format("Total Unpaid Fines: ₱%.2f", totalUnpaidFines));
                        }

                        // Remove the row from the table
                        tableModel.removeRow(modelRow);

                        // Disable delete button
                        deleteFineButton.setEnabled(false);

                        JOptionPane.showMessageDialog(this,
                            "Fine deleted successfully.",
                            "Success",
                            JOptionPane.INFORMATION_MESSAGE);
                    } else {
                        JOptionPane.showMessageDialog(this,
                            "Failed to delete fine from the database.",
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                    }

                    DatabaseConnection.closeConnection(conn);

                } catch (Exception e) {
                    e.printStackTrace();
                    JOptionPane.showMessageDialog(this,
                        "Error deleting fine: " + e.getMessage(),
                        "Database Error",
                        JOptionPane.ERROR_MESSAGE);
                }
            }
        }
    }


}