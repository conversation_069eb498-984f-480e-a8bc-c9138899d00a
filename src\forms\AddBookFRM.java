package forms;

import java.sql.Connection;
import java.sql.PreparedStatement;
import javax.swing.JOptionPane;
import library.management.system.database.DatabaseConnection;

public class AddBookFRM extends javax.swing.JFrame {

    /**
     * Creates new form AddBookFRM
     */
    public AddBookFRM() {
        initComponents();
        this.setLocationRelativeTo(null);
        this.setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jPanel1 = new javax.swing.JPanel();
        jLabel1 = new javax.swing.JLabel();
        jPanel2 = new javax.swing.JPanel();
        addBTN = new javax.swing.JButton();
        closeBTN = new javax.swing.JButton();
        jLabel2 = new javax.swing.JLabel();
        isbn = new javax.swing.JTextField();
        jLabel3 = new javax.swing.JLabel();
        title = new javax.swing.JTextField();
        jLabel9 = new javax.swing.JLabel(); // Add label for Author
        author = new javax.swing.JTextField(); // Add Author text field
        publisher = new javax.swing.JTextField();
        jLabel4 = new javax.swing.JLabel();
        jLabel5 = new javax.swing.JLabel();
        publication_year = new javax.swing.JTextField();
        jLabel6 = new javax.swing.JLabel();
        genre = new javax.swing.JTextField();
        edition = new javax.swing.JTextField();
        jLabel7 = new javax.swing.JLabel();
        jLabel8 = new javax.swing.JLabel();
        copyright = new javax.swing.JTextField();
        jLabel10 = new javax.swing.JLabel();
        copies = new javax.swing.JTextField();
        jLabel11 = new javax.swing.JLabel();
        statusComboBox = new javax.swing.JComboBox<>();
        jLabel12 = new javax.swing.JLabel();
        acquisitionDate = new javax.swing.JTextField(); // Add acquisitionDate text field
        jLabel13 = new javax.swing.JLabel();
        condition = new javax.swing.JTextField();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        jPanel1.setBackground(new java.awt.Color(204, 204, 204));

        jLabel1.setFont(new java.awt.Font("Segoe UI", 1, 24)); // NOI18N
        jLabel1.setText("ADD BOOK");

        javax.swing.GroupLayout jPanel1Layout = new javax.swing.GroupLayout(jPanel1);
        jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addContainerGap()
                .addComponent(jLabel1)
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );
        jPanel1Layout.setVerticalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel1Layout.createSequentialGroup()
                .addContainerGap(30, Short.MAX_VALUE)
                .addComponent(jLabel1)
                .addContainerGap())
        );

        getContentPane().add(jPanel1);

        jPanel2.setBackground(new java.awt.Color(204, 204, 204));

        addBTN.setFont(new java.awt.Font("Segoe UI", 0, 18)); // NOI18N
        addBTN.setText("ADD");
        addBTN.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                addBTNActionPerformed(evt);
            }
        });

        closeBTN.setFont(new java.awt.Font("Segoe UI", 0, 18)); // NOI18N
        closeBTN.setText("CLOSE");
        closeBTN.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                closeBTNActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout jPanel2Layout = new javax.swing.GroupLayout(jPanel2);
        jPanel2.setLayout(jPanel2Layout);
        jPanel2Layout.setHorizontalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel2Layout.createSequentialGroup()
                .addGap(21, 21, 21)
                .addComponent(closeBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 221, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, 56, Short.MAX_VALUE)
                .addComponent(addBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 197, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(21, 21, 21))
        );
        jPanel2Layout.setVerticalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel2Layout.createSequentialGroup()
                .addContainerGap(30, Short.MAX_VALUE)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(closeBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 36, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(addBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 36, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(30, 30, 30))
        );

        getContentPane().add(jPanel2);

        jLabel2.setText("ISBN");
        getContentPane().add(jLabel2);
        getContentPane().add(isbn);

        jLabel3.setText("Title");
        getContentPane().add(jLabel3);
        getContentPane().add(title);
        
        jLabel9.setText("Author"); // Add author label text
        getContentPane().add(jLabel9);
        getContentPane().add(author); // Add author field to layout
        
        getContentPane().add(publisher);

        jLabel4.setText("Publisher");
        getContentPane().add(jLabel4);

        jLabel5.setText("Publication Year");
        getContentPane().add(jLabel5);
        getContentPane().add(publication_year);

        jLabel6.setText("Genre");
        getContentPane().add(jLabel6);
        getContentPane().add(genre);
        getContentPane().add(edition);

        jLabel7.setText("Edition");
        getContentPane().add(jLabel7);

        jLabel8.setText("Copyright");
        getContentPane().add(jLabel8);
        getContentPane().add(copyright);

        jLabel10.setText("Copies");
        getContentPane().add(jLabel10);
        getContentPane().add(copies);

        jLabel11.setText("Status");
        getContentPane().add(jLabel11);

        statusComboBox.setModel(new javax.swing.DefaultComboBoxModel<>(new String[] { "Available", "Loaned", "Reserved", "Under Repair", "Lost" }));
        getContentPane().add(statusComboBox);

        jLabel12.setText("Acquisition Date");
        getContentPane().add(jLabel12);
        getContentPane().add(acquisitionDate); // Add acquisitionDate field to layout
        
        jLabel13.setText("Condition");
        getContentPane().add(jLabel13);
        getContentPane().add(condition);

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void addBTNActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_addBTNActionPerformed
        // Get values from form fields
        String isbnValue = isbn.getText().trim();
        String titleValue = title.getText().trim();
        String authorValue = author.getText().trim();
        String publisherValue = publisher.getText().trim();
        String pubYearValue = publication_year.getText().trim();
        String copyrightValue = copyright.getText().trim();
        String genreValue = genre.getText().trim();
        String editionValue = edition.getText().trim();
        String copiesValue = copies.getText().trim();
        String statusValue = statusComboBox.getSelectedItem().toString();
        String conditionValue = condition.getText().trim();
        String acquisitionDateStr = acquisitionDate.getText().trim();
        
        // Validate required fields
        if (isbnValue.isEmpty() || titleValue.isEmpty() || authorValue.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "ISBN, Title, and Author are required fields.", 
                "Validation Error", 
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        try {
            // Connect to database
            Connection conn = DatabaseConnection.getConnection();

            // Updated SQL statement with additional fields
            String sql = "INSERT INTO Books (ISBN, Title, Author, Publisher, PublicationYear, Copyright, " +
                         "Genre, Edition, Copies, Status, AcquisitionDate, Condition) " +
                         "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, isbnValue);
            pstmt.setString(2, titleValue);
            pstmt.setString(3, authorValue);
            pstmt.setString(4, publisherValue);
            
            // Handle publication year
            if (!pubYearValue.isEmpty()) {
                try {
                    int pubYear = Integer.parseInt(pubYearValue);
                    pstmt.setInt(5, pubYear);
                } catch (NumberFormatException e) {
                    pstmt.setNull(5, java.sql.Types.INTEGER);
                }
            } else {
                pstmt.setNull(5, java.sql.Types.INTEGER);
            }
            
            pstmt.setString(6, copyrightValue);
            pstmt.setString(7, genreValue);
            pstmt.setString(8, editionValue);
            
            // Handle copies
            if (!copiesValue.isEmpty()) {
                try {
                    int copyCount = Integer.parseInt(copiesValue);
                    pstmt.setInt(9, copyCount);
                } catch (NumberFormatException e) {
                    pstmt.setInt(9, 0); // Default to 0 if invalid number
                }
            } else {
                pstmt.setInt(9, 0); // Default to 0 if empty
            }
            
            pstmt.setString(10, statusValue);
            
            // Handle acquisition date with manual parsing
            if (!acquisitionDateStr.isEmpty()) {
                try {
                    // Try to parse date in YYYY-MM-DD format
                    java.sql.Date sqlDate = java.sql.Date.valueOf(acquisitionDateStr);
                    pstmt.setDate(11, sqlDate);
                } catch (IllegalArgumentException e) {
                    JOptionPane.showMessageDialog(this, 
                        "Invalid date format. Please use YYYY-MM-DD format.",
                        "Date Format Error", 
                        JOptionPane.ERROR_MESSAGE);
                    return;
                }
            } else {
                pstmt.setNull(11, java.sql.Types.DATE);
            }
            
            pstmt.setString(12, conditionValue);

            // Execute the insert
            int rows = pstmt.executeUpdate();
            
            if (rows > 0) {
                JOptionPane.showMessageDialog(this, 
                    "Book added successfully!", 
                    "Success", 
                    JOptionPane.INFORMATION_MESSAGE);
                // Clear form fields
                clearForm();
            } else {
                JOptionPane.showMessageDialog(this, 
                    "Failed to add book.", 
                    "Error", 
                    JOptionPane.ERROR_MESSAGE);
            }
            
            // Close resources
            pstmt.close();
            DatabaseConnection.closeConnection(conn);
            
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "Error adding book: " + e.getMessage(), 
                "Database Error", 
                JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }//GEN-LAST:event_addBTNActionPerformed

    private void closeBTNActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_closeBTNActionPerformed
        dispose();
    }//GEN-LAST:event_closeBTNActionPerformed

    private void clearForm() {
        isbn.setText("");
        title.setText("");
        author.setText("");
        publisher.setText("");
        publication_year.setText("");
        copyright.setText("");
        genre.setText("");
        edition.setText("");
        copies.setText("");
        statusComboBox.setSelectedIndex(0); // Reset to "Available"
        acquisitionDate.setText(""); // Changed from acquisitionDateChooser.setDate(null)
        condition.setText("");
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton addBTN;
    private javax.swing.JTextField acquisitionDate; // Add acquisition date declaration
    private javax.swing.JTextField author; // Add author declaration
    private javax.swing.JButton closeBTN;
    private javax.swing.JTextField condition;
    private javax.swing.JTextField copies;
    private javax.swing.JTextField copyright;
    private javax.swing.JTextField edition;
    private javax.swing.JTextField genre;
    private javax.swing.JTextField isbn;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel10;
    private javax.swing.JLabel jLabel11;
    private javax.swing.JLabel jLabel12;
    private javax.swing.JLabel jLabel13;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JLabel jLabel6;
    private javax.swing.JLabel jLabel7;
    private javax.swing.JLabel jLabel8;
    private javax.swing.JLabel jLabel9; // Add jLabel9 declaration
    private javax.swing.JPanel jPanel1;
    private javax.swing.JPanel jPanel2;
    private javax.swing.JTextField publication_year;
    private javax.swing.JTextField publisher;
    private javax.swing.JComboBox<String> statusComboBox;
    private javax.swing.JTextField title;
    // End of variables declaration//GEN-END:variables
}
