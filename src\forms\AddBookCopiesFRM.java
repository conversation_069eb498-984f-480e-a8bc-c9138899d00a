package forms;

import java.awt.Color;
import java.awt.Font;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.swing.BorderFactory;
import javax.swing.ButtonGroup;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JTextField;
import library.management.system.database.DatabaseConnection;

public class AddBookCopiesFRM extends javax.swing.JFrame {

    private JPanel mainPanel;
    private JPanel headerPanel;
    private JLabel titleLabel;

    private JLabel bookLabel;
    private JComboBox<BookItem> bookComboBox;

    private JLabel copiesLabel;
    private JTextField copiesField;

    private JLabel statusLabel;
    private JComboBox<String> statusComboBox;

    private JLabel acquisitionDateLabel;
    private JTextField acquisitionDateField;

    private JLabel conditionLabel;
    private JRadioButton goodRadio;
    private JRadioButton poorRadio;
    private ButtonGroup conditionGroup;

    private JButton saveButton;
    private JButton cancelButton;

    // Class to hold book information for the combo box
    private static class BookItem {
        private int id;
        private String title;
        private String author;

        public BookItem(int id, String title, String author) {
            this.id = id;
            this.title = title;
            this.author = author;
        }

        public int getId() {
            return id;
        }

        public String getTitle() {
            return title;
        }

        public String getAuthor() {
            return author;
        }

        @Override
        public String toString() {
            return title + " (by " + author + ")";
        }
    }

    public AddBookCopiesFRM() {
        // Remove window decoration (title bar, minimize, maximize, close buttons)
        setUndecorated(true);

        initComponents();
        loadBooks();

        // Center the form on the screen
        setLocationRelativeTo(null);
    }

    private void initComponents() {
        // Set up the main panel
        mainPanel = new JPanel();
        mainPanel.setLayout(null);
        mainPanel.setBackground(new Color(240, 240, 240));

        // Set up the header panel
        headerPanel = new JPanel();
        headerPanel.setLayout(null);
        headerPanel.setBackground(new Color(70, 130, 180));
        headerPanel.setBounds(0, 0, 500, 70);

        // Set up the title label
        titleLabel = new JLabel("Add Book Copies");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBounds(150, 20, 250, 30);
        headerPanel.add(titleLabel);

        // Add the header panel to the main panel
        mainPanel.add(headerPanel);

        // Set up the book selection
        bookLabel = new JLabel("Select Book:");
        bookLabel.setFont(new Font("Arial", Font.BOLD, 14));
        bookLabel.setBounds(30, 90, 150, 25);
        mainPanel.add(bookLabel);

        bookComboBox = new JComboBox<>();
        bookComboBox.setFont(new Font("Arial", Font.PLAIN, 14));
        bookComboBox.setBounds(180, 90, 280, 30);
        mainPanel.add(bookComboBox);

        // Set up the copies field
        copiesLabel = new JLabel("Number of Copies:");
        copiesLabel.setFont(new Font("Arial", Font.BOLD, 14));
        copiesLabel.setBounds(30, 140, 150, 25);
        mainPanel.add(copiesLabel);

        copiesField = new JTextField("1");
        copiesField.setFont(new Font("Arial", Font.PLAIN, 14));
        copiesField.setBounds(180, 140, 100, 30);
        mainPanel.add(copiesField);

        // Set up the status dropdown
        statusLabel = new JLabel("Status:");
        statusLabel.setFont(new Font("Arial", Font.BOLD, 14));
        statusLabel.setBounds(30, 190, 150, 25);
        mainPanel.add(statusLabel);

        // Create a dropdown for status instead of radio buttons
        statusComboBox = new JComboBox<>(new String[] {
            "Available", "Loaned", "Reserved", "Damaged", "Lost"
        });
        statusComboBox.setFont(new Font("Arial", Font.PLAIN, 14));
        statusComboBox.setBounds(180, 190, 150, 30);
        statusComboBox.setSelectedItem("Available"); // Default to Available
        mainPanel.add(statusComboBox);

        // Set up the acquisition date field
        acquisitionDateLabel = new JLabel("Acquisition Date:");
        acquisitionDateLabel.setFont(new Font("Arial", Font.BOLD, 14));
        acquisitionDateLabel.setBounds(30, 240, 150, 25);
        mainPanel.add(acquisitionDateLabel);

        // Set default date to today
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String today = dateFormat.format(new Date());

        acquisitionDateField = new JTextField(today);
        acquisitionDateField.setFont(new Font("Arial", Font.PLAIN, 14));
        acquisitionDateField.setBounds(180, 240, 150, 30);
        mainPanel.add(acquisitionDateField);

        // Set up the condition radio buttons
        conditionLabel = new JLabel("Condition:");
        conditionLabel.setFont(new Font("Arial", Font.BOLD, 14));
        conditionLabel.setBounds(30, 290, 150, 25);
        mainPanel.add(conditionLabel);

        conditionGroup = new ButtonGroup();

        goodRadio = new JRadioButton("Good");
        goodRadio.setFont(new Font("Arial", Font.PLAIN, 14));
        goodRadio.setBounds(180, 290, 100, 25);
        goodRadio.setBackground(new Color(240, 240, 240));
        goodRadio.setSelected(true);
        conditionGroup.add(goodRadio);
        mainPanel.add(goodRadio);

        poorRadio = new JRadioButton("Poor");
        poorRadio.setFont(new Font("Arial", Font.PLAIN, 14));
        poorRadio.setBounds(290, 290, 100, 25);
        poorRadio.setBackground(new Color(240, 240, 240));
        conditionGroup.add(poorRadio);
        mainPanel.add(poorRadio);

        // Set up the save button
        saveButton = new JButton("SAVE");
        saveButton.setFont(new Font("Arial", Font.BOLD, 14));
        saveButton.setBackground(new Color(70, 130, 180));
        saveButton.setForeground(Color.WHITE);
        saveButton.setBounds(100, 350, 120, 40);
        saveButton.addActionListener(e -> saveBookCopy());
        mainPanel.add(saveButton);

        // Set up the cancel button
        cancelButton = new JButton("CANCEL");
        cancelButton.setFont(new Font("Arial", Font.BOLD, 14));
        cancelButton.setBackground(new Color(180, 180, 180));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setBounds(280, 350, 120, 40);
        cancelButton.addActionListener(e -> dispose());
        mainPanel.add(cancelButton);

        // Add the main panel to the frame
        getContentPane().add(mainPanel);

        // Set the frame properties
        setSize(500, 420);
        setResizable(false);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
    }

    private void loadBooks() {
        try {
            Connection conn = DatabaseConnection.getConnection();
            String sql = "SELECT BookID, Title, Author FROM Books ORDER BY Title";
            PreparedStatement pstmt = conn.prepareStatement(sql);
            ResultSet rs = pstmt.executeQuery();

            DefaultComboBoxModel<BookItem> model = new DefaultComboBoxModel<>();

            while (rs.next()) {
                int id = rs.getInt("BookID");
                String title = rs.getString("Title");
                String author = rs.getString("Author");

                model.addElement(new BookItem(id, title, author));
            }

            bookComboBox.setModel(model);

            rs.close();
            pstmt.close();
            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "Error loading books: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    private void saveBookCopy() {
        try {
            // Validate book selection
            if (bookComboBox.getSelectedItem() == null) {
                JOptionPane.showMessageDialog(this,
                    "Please select a book.",
                    "Validation Error",
                    JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Get the selected book
            BookItem selectedBook = (BookItem) bookComboBox.getSelectedItem();
            int bookId = selectedBook.getId();

            // Validate copies
            int copies;
            try {
                copies = Integer.parseInt(copiesField.getText().trim());
                if (copies <= 0) {
                    JOptionPane.showMessageDialog(this,
                        "Number of copies must be greater than 0.",
                        "Validation Error",
                        JOptionPane.ERROR_MESSAGE);
                    copiesField.requestFocus();
                    return;
                }
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(this,
                    "Please enter a valid number for copies.",
                    "Validation Error",
                    JOptionPane.ERROR_MESSAGE);
                copiesField.requestFocus();
                return;
            }

            // Get status from dropdown
            String status = (String) statusComboBox.getSelectedItem();

            // Validate and parse acquisition date
            java.sql.Date acquisitionDate = null;
            String acquisitionDateStr = acquisitionDateField.getText().trim();

            if (!acquisitionDateStr.isEmpty()) {
                try {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    dateFormat.setLenient(false);
                    java.util.Date parsedDate = dateFormat.parse(acquisitionDateStr);
                    acquisitionDate = new java.sql.Date(parsedDate.getTime());
                } catch (Exception e) {
                    JOptionPane.showMessageDialog(this,
                        "Invalid date format. Please use YYYY-MM-DD format.",
                        "Validation Error",
                        JOptionPane.ERROR_MESSAGE);
                    acquisitionDateField.requestFocus();
                    return;
                }
            }

            // Get condition
            String condition = goodRadio.isSelected() ? "Good" : "Poor";

            // Save to database
            Connection conn = DatabaseConnection.getConnection();

            String sql = "INSERT INTO BookCopies (BookID, Status, AcquisitionDate, Condition, Copies) VALUES (?, ?, ?, ?, ?)";
            PreparedStatement pstmt = conn.prepareStatement(sql);

            pstmt.setInt(1, bookId);
            pstmt.setString(2, status);

            if (acquisitionDate != null) {
                pstmt.setDate(3, acquisitionDate);
            } else {
                pstmt.setNull(3, java.sql.Types.DATE);
            }

            pstmt.setString(4, condition);
            pstmt.setInt(5, copies);

            int rowsAffected = pstmt.executeUpdate();

            pstmt.close();
            DatabaseConnection.closeConnection(conn);

            if (rowsAffected > 0) {
                JOptionPane.showMessageDialog(this,
                    "Book copy added successfully!",
                    "Success",
                    JOptionPane.INFORMATION_MESSAGE);
                dispose(); // Close the form
            } else {
                JOptionPane.showMessageDialog(this,
                    "Failed to add book copy.",
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
            }

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "Error adding book copy: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }
}
