package dashboard_page;

import dashboard_page.panels.*;
import javax.swing.*;
import java.awt.*;


public class Dashboard extends javax.swing.JFrame {

    // Content panels
    private DashboardContentPanel dashboardPanel;
    private BooksContentPanel booksPanel;
    private LoansContentPanel loansPanel;
    private FineManagementContentPanel finePanel; // Fine management panel
    private UsersContentPanel usersPanel; // Full UsersContentPanel with database functionality

    /**
     * Creates new form Dashboard
     */
    public Dashboard() {
        // Remove window decoration (title bar, minimize, maximize, close buttons)
        // Must be called before initComponents() makes the frame displayable
        setUndecorated(true);

        initComponents();

        // Initialize content panels
        initializePanels();

        // Set up action listeners for navigation buttons
        setupActionListeners();

        // Show dashboard panel by default
        showPanel(dashboardPanel);

        // Set the frame to be maximized
        setExtendedState(JFrame.MAXIMIZED_BOTH);
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jPanel1 = new javax.swing.JPanel();
        dashboardBTN = new javax.swing.JButton();
        loanBTN = new javax.swing.JButton();
        logoutBTN = new javax.swing.JButton();
        fineBTN = new javax.swing.JButton();
        bookBTN = new javax.swing.JButton();
        userBTN = new javax.swing.JButton();
        main_panel = new javax.swing.JPanel();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);
        setUndecorated(true);

        jPanel1.setBackground(new java.awt.Color(51, 102, 255));
        jPanel1.setPreferredSize(new java.awt.Dimension(300, 1080));

        dashboardBTN.setFont(new java.awt.Font("Segoe UI", 0, 18)); // NOI18N
        dashboardBTN.setText("Dashboard");

        loanBTN.setFont(new java.awt.Font("Segoe UI", 0, 18)); // NOI18N
        loanBTN.setText("Borrow Management");

        logoutBTN.setFont(new java.awt.Font("Segoe UI", 0, 18)); // NOI18N
        logoutBTN.setText("Logout");

        fineBTN.setFont(new java.awt.Font("Segoe UI", 0, 18)); // NOI18N
        fineBTN.setText("Fine Management");

        bookBTN.setFont(new java.awt.Font("Segoe UI", 0, 18)); // NOI18N
        bookBTN.setText("Book Management");

        userBTN.setFont(new java.awt.Font("Segoe UI", 0, 18)); // NOI18N
        userBTN.setText("User Management");

        javax.swing.GroupLayout jPanel1Layout = new javax.swing.GroupLayout(jPanel1);
        jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addGap(18, 18, 18)
                .addGroup(jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(logoutBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(dashboardBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(loanBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(fineBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(bookBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(userBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addContainerGap(18, Short.MAX_VALUE))
        );
        jPanel1Layout.setVerticalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addGap(240, 240, 240)
                .addComponent(dashboardBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(20, 20, 20)
                .addComponent(bookBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(20, 20, 20)
                .addComponent(loanBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(20, 20, 20)
                .addComponent(fineBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(20, 20, 20)
                .addComponent(userBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, 350, Short.MAX_VALUE)
                .addComponent(logoutBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(40, 40, 40))
        );

        main_panel.setPreferredSize(new java.awt.Dimension(1620, 1080));
        main_panel.setLayout(new BorderLayout());

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(jPanel1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(main_panel, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addComponent(jPanel1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
            .addComponent(main_panel, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents


    /**
     * Initialize all content panels
     */
    private void initializePanels() {
        dashboardPanel = new DashboardContentPanel();
        booksPanel = new BooksContentPanel();
        loansPanel = new LoansContentPanel();

        try {
            usersPanel = new UsersContentPanel();
            System.out.println("UsersContentPanel created successfully");
        } catch (Exception e) {
            System.err.println("Error creating UsersContentPanel: " + e.getMessage());
            e.printStackTrace();
            // Create a placeholder panel if the UsersContentPanel fails to load
            JPanel placeholderPanel = createPlaceholderPanel("User Management");
            usersPanel = new UsersContentPanel();
            // Replace the content with the placeholder content
            usersPanel.removeAll();
            usersPanel.setLayout(new BorderLayout());
            usersPanel.add(placeholderPanel, BorderLayout.CENTER);
        }

        // Create the Fine Management panel
        finePanel = new FineManagementContentPanel();
    }

    /**
     * Set up action listeners for all navigation buttons
     */
    private void setupActionListeners() {
        // Dashboard button
        dashboardBTN.addActionListener(e -> showPanel(dashboardPanel));

        // Book Management button
        bookBTN.addActionListener(e -> showPanel(booksPanel));

        // Loan Management button
        loanBTN.addActionListener(e -> showPanel(loansPanel));

        // Fine Management button
        fineBTN.addActionListener(e -> showPanel(finePanel));

        // User Management button
        userBTN.addActionListener(e -> {
            System.out.println("User Management button clicked");
            try {
                if (usersPanel == null) {
                    System.out.println("UsersPanel is null, creating a new one");
                    usersPanel = new UsersContentPanel();
                }
                System.out.println("Showing UsersPanel: " + usersPanel.getClass().getName());
                showPanel(usersPanel);
            } catch (Exception ex) {
                System.err.println("Error showing UsersPanel: " + ex.getMessage());
                ex.printStackTrace();
                JOptionPane.showMessageDialog(
                    this,
                    "Error showing User Management panel: " + ex.getMessage(),
                    "Error",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        });

        // Logout button
        logoutBTN.addActionListener(e -> logout());
    }

    /**
     * Show the specified panel in the main content area
     * @param panel The panel to display
     */
    private void showPanel(JPanel panel) {
        // Clear the main panel
        main_panel.removeAll();

        // Create a refresh button
        JButton refreshButton = new JButton("Refresh Data");
        refreshButton.addActionListener(e -> refreshCurrentPanel());

        // Create a top panel for the refresh button
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        topPanel.add(refreshButton);

        // Create a wrapper panel to hold both the content and the refresh button
        JPanel wrapperPanel = new JPanel(new BorderLayout());
        wrapperPanel.add(topPanel, BorderLayout.NORTH);
        wrapperPanel.add(panel, BorderLayout.CENTER);

        // Add the wrapper panel to the main panel
        main_panel.add(wrapperPanel, BorderLayout.CENTER);

        // Refresh the panel
        main_panel.revalidate();
        main_panel.repaint();

        // Debug message to confirm panel switch
        System.out.println("Switched to panel: " + panel.getClass().getSimpleName());

        // Refresh data if it's a dashboard panel
        if (panel instanceof DashboardContentPanel) {
            ((DashboardContentPanel) panel).loadDashboardData();
        }
    }

    /**
     * Refresh the currently displayed panel
     */
    public void refreshCurrentPanel() {
        Component[] components = main_panel.getComponents();
        if (components.length > 0 && components[0] instanceof JPanel) {
            JPanel wrapperPanel = (JPanel) components[0];

            // Get the content panel from the wrapper (it's in the CENTER position)
            Component[] wrapperComponents = wrapperPanel.getComponents();
            for (Component comp : wrapperComponents) {
                if (comp instanceof JPanel && wrapperPanel.getLayout() instanceof BorderLayout) {
                    Object constraints = ((BorderLayout)wrapperPanel.getLayout()).getConstraints(comp);
                    if (constraints == BorderLayout.CENTER && comp instanceof JPanel) {
                        JPanel contentPanel = (JPanel) comp;

                        if (contentPanel instanceof DashboardContentPanel) {
                            ((DashboardContentPanel) contentPanel).loadDashboardData();
                            System.out.println("Refreshed Dashboard panel");
                        } else if (contentPanel instanceof BooksContentPanel) {
                            // Refresh books panel if needed
                            System.out.println("Refreshed Books panel");
                        } else if (contentPanel instanceof LoansContentPanel) {
                            // Refresh loans panel if needed
                            System.out.println("Refreshed Loans panel");
                        } else if (contentPanel instanceof UsersContentPanel) {
                            // Refresh users panel
                            ((UsersContentPanel) contentPanel).loadUsersData();
                            System.out.println("Refreshed Users panel");
                        } else if (contentPanel instanceof FineManagementContentPanel) {
                            // Refresh fines panel
                            ((FineManagementContentPanel) contentPanel).loadFinesData();
                            System.out.println("Refreshed Fines panel");
                        }
                        break;
                    }
                }
            }
        }
    }

    /**
     * Create a placeholder panel for features not yet implemented
     * @param title The title to display in the panel
     * @return A simple JPanel with a message
     */
    private JPanel createPlaceholderPanel(String title) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(Color.WHITE);

        JLabel titleLabel = new JLabel(title + " - Coming Soon");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setHorizontalAlignment(JLabel.CENTER);

        JLabel messageLabel = new JLabel("This feature is not yet implemented");
        messageLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        messageLabel.setHorizontalAlignment(JLabel.CENTER);

        JPanel centerPanel = new JPanel(new GridLayout(2, 1));
        centerPanel.setBackground(Color.WHITE);
        centerPanel.add(titleLabel);
        centerPanel.add(messageLabel);

        panel.add(centerPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Handle logout action
     */
    private void logout() {
        int confirm = JOptionPane.showConfirmDialog(
            this,
            "Are you sure you want to logout?",
            "Confirm Logout",
            JOptionPane.YES_NO_OPTION
        );

        if (confirm == JOptionPane.YES_OPTION) {
            // Close the dashboard
            this.dispose();

            // Show the login form
            try {
                // Use reflection to avoid direct dependency
                Class<?> loginClass = Class.forName("library.management.system.login_register.Login");
                Object login = loginClass.getDeclaredConstructor().newInstance();
                loginClass.getMethod("setVisible", boolean.class).invoke(login, true);
            } catch (Exception ex) {
                System.err.println("Error opening login form: " + ex.getMessage());
                ex.printStackTrace();
            }
        }
    }

    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(Dashboard.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(Dashboard.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(Dashboard.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(Dashboard.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new Dashboard().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton bookBTN;
    private javax.swing.JButton dashboardBTN;
    private javax.swing.JButton fineBTN;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JButton loanBTN;
    private javax.swing.JButton logoutBTN;
    private javax.swing.JPanel main_panel;
    private javax.swing.JButton userBTN;
    // End of variables declaration//GEN-END:variables
}
