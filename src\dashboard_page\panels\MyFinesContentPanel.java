package dashboard_page.panels;

import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import library.management.system.database.DatabaseConnection;

public class MyFinesContentPanel extends JPanel {

    private JTable finesTable;
    private DefaultTableModel tableModel;
    private JButton refreshButton;
    private JButton payButton;
    private JLabel totalLabel; // Added to store reference to the total label

    public MyFinesContentPanel() {
        initComponents();
        loadFinesData();
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));
        setBackground(new Color(240, 240, 240));

        // Create header panel
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(240, 240, 240));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 10, 20));

        JLabel titleLabel = new JLabel("My Fines");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 28));
        titleLabel.setForeground(new Color(70, 70, 70));

        JLabel subtitleLabel = new JLabel("View and pay your library fines");
        subtitleLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        subtitleLabel.setForeground(new Color(120, 120, 120));

        headerPanel.add(titleLabel, BorderLayout.NORTH);
        headerPanel.add(subtitleLabel, BorderLayout.CENTER);

        // Create button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.setBackground(new Color(240, 240, 240));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));

        refreshButton = new JButton("Refresh");
        refreshButton.setFont(new Font("Arial", Font.BOLD, 16));
        refreshButton.setPreferredSize(new Dimension(120, 40));
        refreshButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                loadFinesData();
            }
        });

        payButton = new JButton("Pay Selected Fine");
        payButton.setFont(new Font("Arial", Font.BOLD, 16));
        payButton.setPreferredSize(new Dimension(200, 40));
        payButton.setEnabled(false);
        payButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                paySelectedFine();
            }
        });

        buttonPanel.add(refreshButton);
        buttonPanel.add(payButton);

        // Create table panel
        JPanel tablePanel = new JPanel(new BorderLayout());
        tablePanel.setBackground(Color.WHITE);
        tablePanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // Create table model
        String[] columns = {"Fine ID", "Book Title", "Fine Date", "Amount", "Reason", "Status"};
        tableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        finesTable = new JTable(tableModel);
        finesTable.setRowHeight(40);
        finesTable.setFont(new Font("Arial", Font.PLAIN, 14));
        finesTable.getTableHeader().setFont(new Font("Arial", Font.BOLD, 14));
        finesTable.getTableHeader().setBackground(new Color(240, 240, 240));
        finesTable.getTableHeader().setPreferredSize(new Dimension(0, 40));
        finesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        finesTable.setAutoCreateRowSorter(true);

        // Add selection listener to enable/disable pay button
        finesTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                int selectedRow = finesTable.getSelectedRow();
                if (selectedRow >= 0) {
                    String status = (String) finesTable.getValueAt(selectedRow, 5);
                    payButton.setEnabled("Unpaid".equals(status));
                } else {
                    payButton.setEnabled(false);
                }
            }
        });

        // Set column widths
        finesTable.getColumnModel().getColumn(0).setPreferredWidth(80);  // Fine ID
        finesTable.getColumnModel().getColumn(1).setPreferredWidth(300); // Book Title
        finesTable.getColumnModel().getColumn(2).setPreferredWidth(120); // Fine Date
        finesTable.getColumnModel().getColumn(3).setPreferredWidth(100); // Amount
        finesTable.getColumnModel().getColumn(4).setPreferredWidth(200); // Reason
        finesTable.getColumnModel().getColumn(5).setPreferredWidth(100); // Status

        // Add custom renderer for status and amount columns
        finesTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (c instanceof JLabel) {
                    JLabel label = (JLabel) c;
                    label.setBorder(BorderFactory.createEmptyBorder(0, 8, 0, 8));

                    // Center align Fine ID, Fine Date, Amount, and Status columns
                    if (column == 0 || column == 2 || column == 3 || column == 5) {
                        label.setHorizontalAlignment(JLabel.CENTER);

                        // Format amount column
                        if (column == 3 && value != null) {
                            if (!value.toString().startsWith("₱")) {
                                label.setText("₱" + value);
                            }
                        }

                        // Color status column
                        if (column == 5 && value != null) {
                            String status = value.toString();
                            if ("Paid".equals(status)) {
                                label.setForeground(new Color(0, 128, 0)); // Green
                            } else if ("Unpaid".equals(status)) {
                                label.setForeground(new Color(220, 0, 0)); // Red
                            }
                        }
                    } else {
                        // Left align other columns
                        label.setHorizontalAlignment(JLabel.LEFT);
                    }
                }

                return c;
            }
        });

        JScrollPane scrollPane = new JScrollPane(finesTable);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());

        tablePanel.add(scrollPane, BorderLayout.CENTER);

        // Create summary panel
        JPanel summaryPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        summaryPanel.setBackground(Color.WHITE);
        summaryPanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 0, 0));

        totalLabel = new JLabel("Total Unpaid Fines: ₱0.00");
        totalLabel.setFont(new Font("Arial", Font.BOLD, 16));
        totalLabel.setForeground(new Color(70, 70, 70));

        summaryPanel.add(totalLabel);

        tablePanel.add(summaryPanel, BorderLayout.SOUTH);

        // Create a panel to hold the header and buttons
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBackground(new Color(240, 240, 240));
        topPanel.add(headerPanel, BorderLayout.NORTH);
        topPanel.add(buttonPanel, BorderLayout.CENTER);

        // Create main panel to hold everything
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(240, 240, 240));
        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(tablePanel, BorderLayout.CENTER);

        add(mainPanel, BorderLayout.CENTER);
    }

    private void loadFinesData() {
        try {
            tableModel.setRowCount(0);
            double totalUnpaidFines = 0.0;

            Connection conn = DatabaseConnection.getConnection();

            // Get the current user ID
            int currentUserId = library.management.system.login_register.Login.getCurrentUserId();

            // Query to get all fines for the current user
            String query = "SELECT f.FineID, b.Title, f.FineDate, f.Amount, f.Reason, f.Status " +
                          "FROM Fines f " +
                          "JOIN Loans l ON f.LoanID = l.LoanID " +
                          "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                          "JOIN Books b ON bc.BookID = b.BookID " +
                          "WHERE f.UserID = ? " +
                          "ORDER BY f.FineDate DESC";

            PreparedStatement stmt = conn.prepareStatement(query);
            stmt.setInt(1, currentUserId);

            ResultSet rs = stmt.executeQuery();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

            boolean foundFines = false;
            while (rs.next()) {
                foundFines = true;
                int fineId = rs.getInt("FineID");
                String bookTitle = rs.getString("Title");
                Date fineDate = rs.getDate("FineDate");
                double amount = rs.getDouble("Amount");
                String reason = rs.getString("Reason");
                String status = rs.getString("Status");

                // Format the date
                String formattedDate = fineDate != null ? dateFormat.format(fineDate) : "";

                tableModel.addRow(new Object[]{
                    String.valueOf(fineId),
                    bookTitle,
                    formattedDate,
                    String.format("%.2f", amount),
                    reason,
                    status
                });

                // Calculate total unpaid fines
                if ("Unpaid".equals(status)) {
                    totalUnpaidFines += amount;
                }
            }

            // Update the total fines label
            totalLabel.setText(String.format("Total Unpaid Fines: ₱%.2f", totalUnpaidFines));

            // If no fines found, show a message
            if (!foundFines) {
                // No need to show a message dialog, just leave the table empty
                System.out.println("No fines found for user ID: " + currentUserId);
            }

            rs.close();
            stmt.close();
            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "Error loading fines: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    private void paySelectedFine() {
        int selectedRow = finesTable.getSelectedRow();
        if (selectedRow >= 0) {
            // Convert view row index to model row index in case table is sorted
            int modelRow = finesTable.convertRowIndexToModel(selectedRow);

            // Get the fine ID and amount from the selected row
            int fineId = Integer.parseInt(tableModel.getValueAt(modelRow, 0).toString());
            String amount = (String) tableModel.getValueAt(modelRow, 3);
            if (amount.startsWith("$")) {
                amount = amount.substring(1);
            }

            int confirm = JOptionPane.showConfirmDialog(
                this,
                "Are you sure you want to pay the fine of ₱" + amount + "?",
                "Confirm Payment",
                JOptionPane.YES_NO_OPTION
            );

            if (confirm == JOptionPane.YES_OPTION) {
                try {
                    // Update the fine status in the database
                    Connection conn = DatabaseConnection.getConnection();

                    String updateQuery = "UPDATE Fines SET Status = 'Paid', PaymentDate = CURRENT_DATE WHERE FineID = ?";
                    PreparedStatement stmt = conn.prepareStatement(updateQuery);
                    stmt.setInt(1, fineId);

                    int rowsAffected = stmt.executeUpdate();
                    stmt.close();
                    DatabaseConnection.closeConnection(conn);

                    if (rowsAffected > 0) {
                        // Update the table
                        tableModel.setValueAt("Paid", modelRow, 5);
                        payButton.setEnabled(false);

                        // Recalculate total unpaid fines
                        double totalUnpaidFines = 0.0;
                        for (int i = 0; i < tableModel.getRowCount(); i++) {
                            String status = (String) tableModel.getValueAt(i, 5);
                            if ("Unpaid".equals(status)) {
                                String amountStr = (String) tableModel.getValueAt(i, 3);
                                if (amountStr.startsWith("₱")) {
                                    amountStr = amountStr.substring(1);
                                }
                                totalUnpaidFines += Double.parseDouble(amountStr);
                            }
                        }

                        // Update the total fines label
                        totalLabel.setText(String.format("Total Unpaid Fines: ₱%.2f", totalUnpaidFines));

                        JOptionPane.showMessageDialog(
                            this,
                            "Fine payment processed successfully.",
                            "Payment Successful",
                            JOptionPane.INFORMATION_MESSAGE
                        );
                    } else {
                        JOptionPane.showMessageDialog(
                            this,
                            "Failed to update fine status. Please try again.",
                            "Payment Failed",
                            JOptionPane.ERROR_MESSAGE
                        );
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    JOptionPane.showMessageDialog(
                        this,
                        "Error processing payment: " + e.getMessage(),
                        "Payment Error",
                        JOptionPane.ERROR_MESSAGE
                    );
                }
            }
        }
    }
}
