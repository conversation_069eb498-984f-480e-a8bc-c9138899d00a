<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="undecorated" type="boolean" value="true"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="main_panel" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="0" pref="1614" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
                  <Component id="main_panel" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="ff" green="66" red="33" type="rgb"/>
        </Property>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[300, 1080]"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="18" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Component id="logoutBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="dashboardBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="copiesBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="loanBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="fineBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="bookBTN" min="-2" pref="264" max="-2" attributes="0"/>
                      <Component id="userBTN" min="-2" pref="264" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace pref="18" max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="240" max="-2" attributes="0"/>
                  <Component id="dashboardBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  <Component id="bookBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  <Component id="copiesBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  <Component id="loanBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  <Component id="fineBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
                  <Component id="userBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace pref="350" max="32767" attributes="0"/>
                  <Component id="logoutBTN" min="-2" pref="50" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JButton" name="dashboardBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Dashboard"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="copiesBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Manage Book Copies"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="loanBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Borrow Management"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="logoutBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Logout"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="fineBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Fine Management"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="bookBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Book Management"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="userBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="User Management"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="main_panel">
      <Properties>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[0, 0]"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <EmptySpace min="0" pref="0" max="32767" attributes="0"/>
          </Group>
        </DimensionLayout>
      </Layout>
    </Container>
  </SubComponents>
</Form>
