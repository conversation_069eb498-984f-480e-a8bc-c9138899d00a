package library.management.system.login_register;

public class DashboardLauncher {

    /**
     * Launches the Dashboard application for librarians
     */
    public static void launchDashboard() {
        try {
            // Use reflection to avoid direct dependency on the Dashboard class
            Class<?> dashboardClass = Class.forName("dashboard_page.Dashboard");
            Object dashboard = dashboardClass.getDeclaredConstructor().newInstance();

            // Call setVisible(true) using reflection
            dashboardClass.getMethod("setVisible", boolean.class).invoke(dashboard, true);

            System.out.println("Librarian Dashboard launched successfully");
        } catch (Exception e) {
            System.err.println("Error launching Dashboard: " + e.getMessage());
            e.printStackTrace();

            // Show error message to the user
            javax.swing.JOptionPane.showMessageDialog(null,
                "Error launching Dashboard: " + e.getMessage(),
                "Dashboard Launch Error",
                javax.swing.JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Launches the User Dashboard application for regular users
     */
    public static void launchUserDashboard() {
        try {
            // Use reflection to avoid direct dependency on the UserDashboard class
            Class<?> userDashboardClass = Class.forName("dashboard_page.UserDashboard");
            Object userDashboard = userDashboardClass.getDeclaredConstructor().newInstance();

            // Call setVisible(true) using reflection
            userDashboardClass.getMethod("setVisible", boolean.class).invoke(userDashboard, true);

            System.out.println("User Dashboard launched successfully");
        } catch (Exception e) {
            System.err.println("Error launching User Dashboard: " + e.getMessage());
            e.printStackTrace();

            // Show error message to the user
            javax.swing.JOptionPane.showMessageDialog(null,
                "Error launching User Dashboard: " + e.getMessage(),
                "User Dashboard Launch Error",
                javax.swing.JOptionPane.ERROR_MESSAGE);
        }
    }
}
