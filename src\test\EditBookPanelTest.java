package test;

import forms.EditBookPanel;
import library.management.system.database.DatabaseConnection;
import javax.swing.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

/**
 * Simple test class to verify EditBookPanel deadlock fixes
 */
public class EditBookPanelTest {
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // Test database connection first
                System.out.println("Testing database connection...");
                Connection conn = DatabaseConnection.getConnection();
                if (conn != null) {
                    System.out.println("Database connection successful!");
                    DatabaseConnection.closeConnection(conn);
                } else {
                    System.out.println("Database connection failed!");
                    return;
                }
                
                // Create a test book first
                int testBookId = createTestBook();
                if (testBookId > 0) {
                    System.out.println("Created test book with ID: " + testBookId);
                    
                    // Test EditBookPanel
                    JFrame testFrame = new JFrame("EditBookPanel Test");
                    testFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
                    
                    EditBookPanel editPanel = new EditBookPanel(testBookId, new EditBookPanel.BookEditedListener() {
                        @Override
                        public void onBookEdited() {
                            System.out.println("Book edited successfully!");
                        }
                    });
                    
                    testFrame.add(editPanel);
                    testFrame.pack();
                    testFrame.setLocationRelativeTo(null);
                    testFrame.setVisible(true);
                    
                    System.out.println("EditBookPanel test window opened. Try editing the book to test deadlock fixes.");
                } else {
                    System.out.println("Failed to create test book!");
                }
                
            } catch (Exception e) {
                System.err.println("Test failed: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    private static int createTestBook() {
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DatabaseConnection.getConnection();
            
            String sql = "INSERT INTO Books (ISBN, Title, Author, Publisher, PublicationYear, Genre, Copies, Status, Condition) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            pstmt = conn.prepareStatement(sql, PreparedStatement.RETURN_GENERATED_KEYS);
            pstmt.setString(1, "TEST-" + System.currentTimeMillis());
            pstmt.setString(2, "Test Book for EditPanel");
            pstmt.setString(3, "Test Author");
            pstmt.setString(4, "Test Publisher");
            pstmt.setInt(5, 2024);
            pstmt.setString(6, "Test");
            pstmt.setInt(7, 1);
            pstmt.setString(8, "Available");
            pstmt.setString(9, "Good");
            
            int rowsAffected = pstmt.executeUpdate();
            
            if (rowsAffected > 0) {
                var rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
            
        } catch (Exception e) {
            System.err.println("Error creating test book: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (pstmt != null) {
                try { pstmt.close(); } catch (SQLException e) { /* ignore */ }
            }
            DatabaseConnection.closeConnection(conn);
        }
        
        return -1;
    }
}
