/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/GUIForms/JFrame.java to edit this template
 */
package forms;

import javax.swing.JOptionPane;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Date;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import library.management.system.database.DatabaseConnection;

public class EditBookCopiesFRM extends javax.swing.JFrame {

    private String copyId;
    private String bookId;
    private javax.swing.JTextField copiesField;

    /**
     * Creates new form EditBookCopiesFRM
     */
    public EditBookCopiesFRM() {
        // Remove window decoration (title bar, minimize, maximize, close buttons)
        // Must be called before initComponents() makes the frame displayable
        setUndecorated(true);

        initComponents();
    }

    /**
     * Creates new form EditBookCopiesFRM with book copy details using the new VARCHAR condition
     */
    public EditBookCopiesFRM(String copyId, String bookId, String title, String author,
                           String status, Date acquisitionDate, String condition, int copies) {
        // Remove window decoration (title bar, minimize, maximize, close buttons)
        // Must be called before initComponents() makes the frame displayable
        setUndecorated(true);

        initComponents();

        // Store IDs for later use
        this.copyId = copyId;
        this.bookId = bookId;

        // Set values to form fields
        titleLabel.setText(title);
        authorLabel.setText(author);

        // Set copies count
        copiesField.setText(String.valueOf(copies));

        // Set status in dropdown
        statusComboBox.setSelectedItem(status);

        // Set acquisition date
        if (acquisitionDate != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            acquisitionDateField.setText(dateFormat.format(acquisitionDate));
        }

        // Set condition based on string value
        if ("Good".equals(condition)) {
            goodRadio.setSelected(true);
        } else if ("Poor".equals(condition) || "Very Poor".equals(condition) || "Damaged".equals(condition)) {
            poorRadio.setSelected(true);
        } else {
            // Default to Good if condition is not recognized
            goodRadio.setSelected(true);
        }
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jPanel1 = new javax.swing.JPanel();
        jLabel1 = new javax.swing.JLabel();
        jPanel2 = new javax.swing.JPanel();
        saveButton = new javax.swing.JButton();
        cancelButton = new javax.swing.JButton();

        jLabel2 = new javax.swing.JLabel();
        jLabel3 = new javax.swing.JLabel();
        titleLabel = new javax.swing.JLabel();
        authorLabel = new javax.swing.JLabel();

        jLabel4 = new javax.swing.JLabel();

        jLabel5 = new javax.swing.JLabel();
        acquisitionDateField = new javax.swing.JTextField();

        jLabel6 = new javax.swing.JLabel();
        conditionPanel = new javax.swing.JPanel();
        conditionGroup = new javax.swing.ButtonGroup();
        goodRadio = new javax.swing.JRadioButton();
        poorRadio = new javax.swing.JRadioButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jPanel1.setBackground(new java.awt.Color(204, 204, 204));

        jLabel1.setFont(new java.awt.Font("Segoe UI", 1, 24)); // NOI18N
        jLabel1.setText("Edit Book Copies");

        javax.swing.GroupLayout jPanel1Layout = new javax.swing.GroupLayout(jPanel1);
        jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel1Layout.createSequentialGroup()
                .addGap(152, 152, 152)
                .addComponent(jLabel1)
                .addContainerGap(172, Short.MAX_VALUE))
        );
        jPanel1Layout.setVerticalGroup(
            jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel1Layout.createSequentialGroup()
                .addContainerGap(30, Short.MAX_VALUE)
                .addComponent(jLabel1)
                .addGap(27, 27, 27))
        );

        jPanel2.setBackground(new java.awt.Color(204, 204, 204));

        // Set up the form components
        jLabel2 = new javax.swing.JLabel();
        jLabel2.setText("Book Title:");
        jLabel2.setFont(new java.awt.Font("Segoe UI", 1, 14));

        titleLabel = new javax.swing.JLabel();
        titleLabel.setText("Book Title Here");
        titleLabel.setFont(new java.awt.Font("Segoe UI", 0, 14));

        jLabel3 = new javax.swing.JLabel();
        jLabel3.setText("Author:");
        jLabel3.setFont(new java.awt.Font("Segoe UI", 1, 14));

        authorLabel = new javax.swing.JLabel();
        authorLabel.setText("Author Name Here");
        authorLabel.setFont(new java.awt.Font("Segoe UI", 0, 14));

        // Add Copies field
        javax.swing.JLabel jLabelCopies = new javax.swing.JLabel();
        jLabelCopies.setText("Number of Copies:");
        jLabelCopies.setFont(new java.awt.Font("Segoe UI", 1, 14));

        copiesField = new javax.swing.JTextField();
        copiesField.setFont(new java.awt.Font("Segoe UI", 0, 14));
        copiesField.setText("0");

        jLabel4 = new javax.swing.JLabel();
        jLabel4.setText("Status:");
        jLabel4.setFont(new java.awt.Font("Segoe UI", 1, 14));

        // Create a dropdown for status instead of radio buttons
        statusComboBox = new javax.swing.JComboBox<>(new String[] {
            "Available", "Loaned", "Reserved", "Damaged", "Lost"
        });
        statusComboBox.setFont(new java.awt.Font("Segoe UI", 0, 14));
        statusComboBox.setSelectedItem("Available"); // Default to Available

        jLabel5 = new javax.swing.JLabel();
        jLabel5.setText("Acquisition Date (YYYY-MM-DD):");
        jLabel5.setFont(new java.awt.Font("Segoe UI", 1, 14));

        acquisitionDateField = new javax.swing.JTextField();
        acquisitionDateField.setFont(new java.awt.Font("Segoe UI", 0, 14));

        jLabel6 = new javax.swing.JLabel();
        jLabel6.setText("Condition:");
        jLabel6.setFont(new java.awt.Font("Segoe UI", 1, 14));

        conditionPanel = new javax.swing.JPanel();
        conditionPanel.setLayout(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT, 0, 0));
        conditionPanel.setBackground(new java.awt.Color(204, 204, 204));

        conditionGroup.add(goodRadio);
        goodRadio.setText("Good");
        goodRadio.setFont(new java.awt.Font("Segoe UI", 0, 14));
        goodRadio.setSelected(true);
        goodRadio.setBackground(new java.awt.Color(204, 204, 204));
        conditionPanel.add(goodRadio);

        conditionGroup.add(poorRadio);
        poorRadio.setText("Poor");
        poorRadio.setFont(new java.awt.Font("Segoe UI", 0, 14));
        poorRadio.setBackground(new java.awt.Color(204, 204, 204));
        poorRadio.setMargin(new java.awt.Insets(2, 20, 2, 2));
        conditionPanel.add(poorRadio);

        saveButton = new javax.swing.JButton();
        saveButton.setText("SAVE CHANGES");
        saveButton.setFont(new java.awt.Font("Segoe UI", 1, 14));
        saveButton.setBackground(new java.awt.Color(0, 102, 204));
        saveButton.setForeground(new java.awt.Color(255, 255, 255));
        saveButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                saveButtonActionPerformed(evt);
            }
        });

        cancelButton = new javax.swing.JButton();
        cancelButton.setText("CANCEL");
        cancelButton.setFont(new java.awt.Font("Segoe UI", 1, 14));
        cancelButton.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                cancelButtonActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout jPanel2Layout = new javax.swing.GroupLayout(jPanel2);
        jPanel2.setLayout(jPanel2Layout);
        jPanel2Layout.setHorizontalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel2Layout.createSequentialGroup()
                .addGap(20, 20, 20)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(jPanel2Layout.createSequentialGroup()
                        .addComponent(jLabel2)
                        .addGap(18, 18, 18)
                        .addComponent(titleLabel))
                    .addGroup(jPanel2Layout.createSequentialGroup()
                        .addComponent(jLabel3)
                        .addGap(18, 18, 18)
                        .addComponent(authorLabel))
                    .addGroup(jPanel2Layout.createSequentialGroup()
                        .addComponent(jLabelCopies)
                        .addGap(18, 18, 18)
                        .addComponent(copiesField, javax.swing.GroupLayout.PREFERRED_SIZE, 80, javax.swing.GroupLayout.PREFERRED_SIZE))
                    .addGroup(jPanel2Layout.createSequentialGroup()
                        .addComponent(jLabel4)
                        .addGap(18, 18, 18)
                        .addComponent(statusComboBox, javax.swing.GroupLayout.PREFERRED_SIZE, 150, javax.swing.GroupLayout.PREFERRED_SIZE))
                    .addGroup(jPanel2Layout.createSequentialGroup()
                        .addComponent(jLabel5)
                        .addGap(18, 18, 18)
                        .addComponent(acquisitionDateField, javax.swing.GroupLayout.PREFERRED_SIZE, 150, javax.swing.GroupLayout.PREFERRED_SIZE))
                    .addGroup(jPanel2Layout.createSequentialGroup()
                        .addComponent(jLabel6)
                        .addGap(18, 18, 18)
                        .addComponent(conditionPanel, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                    .addGroup(jPanel2Layout.createSequentialGroup()
                        .addComponent(saveButton, javax.swing.GroupLayout.PREFERRED_SIZE, 150, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addGap(18, 18, 18)
                        .addComponent(cancelButton, javax.swing.GroupLayout.PREFERRED_SIZE, 150, javax.swing.GroupLayout.PREFERRED_SIZE)))
                .addContainerGap(20, Short.MAX_VALUE))
        );
        jPanel2Layout.setVerticalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel2Layout.createSequentialGroup()
                .addGap(20, 20, 20)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel2)
                    .addComponent(titleLabel))
                .addGap(18, 18, 18)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel3)
                    .addComponent(authorLabel))
                .addGap(18, 18, 18)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabelCopies)
                    .addComponent(copiesField, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel4)
                    .addComponent(statusComboBox, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel5)
                    .addComponent(acquisitionDateField, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(jLabel6)
                    .addComponent(conditionPanel, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(30, 30, 30)
                .addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(saveButton, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(cancelButton, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addContainerGap(20, Short.MAX_VALUE))
        );

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addComponent(jPanel1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
            .addComponent(jPanel2, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(jPanel1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(jPanel2, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void saveButtonActionPerformed(java.awt.event.ActionEvent evt) {
        try {
            // Validate acquisition date format
            String acquisitionDateStr = acquisitionDateField.getText().trim();
            java.sql.Date acquisitionDate = null;

            if (!acquisitionDateStr.isEmpty()) {
                try {
                    java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                    dateFormat.setLenient(false);
                    java.util.Date parsedDate = dateFormat.parse(acquisitionDateStr);
                    acquisitionDate = new java.sql.Date(parsedDate.getTime());
                } catch (Exception e) {
                    JOptionPane.showMessageDialog(this,
                        "Invalid date format. Please use YYYY-MM-DD format.",
                        "Validation Error",
                        JOptionPane.ERROR_MESSAGE);
                    acquisitionDateField.requestFocus();
                    return;
                }
            }

            // Get status from dropdown
            String status = (String) statusComboBox.getSelectedItem();

            // Get condition from radio buttons as a string
            String condition = "Good"; // Default
            if (goodRadio.isSelected()) {
                condition = "Good";
            } else if (poorRadio.isSelected()) {
                condition = "Poor";
            }

            // Get copies count
            int copies = 0;
            try {
                copies = Integer.parseInt(copiesField.getText().trim());
                if (copies < 0) {
                    JOptionPane.showMessageDialog(this,
                        "Number of copies cannot be negative.",
                        "Validation Error",
                        JOptionPane.ERROR_MESSAGE);
                    copiesField.requestFocus();
                    return;
                }
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(this,
                    "Please enter a valid number for copies.",
                    "Validation Error",
                    JOptionPane.ERROR_MESSAGE);
                copiesField.requestFocus();
                return;
            }

            // Check if we're creating a new book copy or updating an existing one
            boolean isNewCopy = copyId.equals("0");

            Connection conn = DatabaseConnection.getConnection();

            // First check if the COPIES column exists in the table
            boolean copiesColumnExists = false;
            try {
                // Try to query the COPIES column to see if it exists
                PreparedStatement checkStmt = conn.prepareStatement(
                    "SELECT COPIES FROM BookCopies WHERE 1=0");
                checkStmt.executeQuery();
                checkStmt.close();
                copiesColumnExists = true;
            } catch (SQLException e) {
                // If we get an error, the column probably doesn't exist
                System.out.println("COPIES column does not exist: " + e.getMessage());
                copiesColumnExists = false;
            }

            String sql;
            PreparedStatement pstmt;

            if (isNewCopy) {
                // INSERT a new book copy
                if (copiesColumnExists) {
                    sql = "INSERT INTO BookCopies (BookID, Status, AcquisitionDate, Condition, Copies) VALUES (?, ?, ?, ?, ?)";
                    pstmt = conn.prepareStatement(sql);

                    try {
                        pstmt.setInt(1, Integer.parseInt(bookId));
                    } catch (NumberFormatException e) {
                        throw new SQLException("Invalid Book ID: " + bookId);
                    }

                    pstmt.setString(2, status);

                    if (acquisitionDate != null) {
                        pstmt.setDate(3, acquisitionDate);
                    } else {
                        pstmt.setNull(3, java.sql.Types.DATE);
                    }

                    // Set condition as VARCHAR since the database column is VARCHAR
                    pstmt.setString(4, condition);
                    pstmt.setInt(5, copies);
                } else {
                    sql = "INSERT INTO BookCopies (BookID, Status, AcquisitionDate, Condition) VALUES (?, ?, ?, ?)";
                    pstmt = conn.prepareStatement(sql);

                    try {
                        pstmt.setInt(1, Integer.parseInt(bookId));
                    } catch (NumberFormatException e) {
                        throw new SQLException("Invalid Book ID: " + bookId);
                    }

                    pstmt.setString(2, status);

                    if (acquisitionDate != null) {
                        pstmt.setDate(3, acquisitionDate);
                    } else {
                        pstmt.setNull(3, java.sql.Types.DATE);
                    }

                    // Set condition as VARCHAR since the database column is VARCHAR
                    pstmt.setString(4, condition);
                }
            } else {
                // UPDATE an existing book copy
                if (copiesColumnExists) {
                    // If COPIES column exists, include it in the update
                    sql = "UPDATE BookCopies SET Status = ?, AcquisitionDate = ?, Condition = ?, Copies = ? WHERE CopyID = ?";
                    pstmt = conn.prepareStatement(sql);

                    pstmt.setString(1, status);

                    if (acquisitionDate != null) {
                        pstmt.setDate(2, acquisitionDate);
                    } else {
                        pstmt.setNull(2, java.sql.Types.DATE);
                    }

                    // Set condition as VARCHAR since the database column is VARCHAR
                    pstmt.setString(3, condition);
                    pstmt.setInt(4, copies);

                    // Handle "NULL" string for copyId
                    try {
                        pstmt.setInt(5, Integer.parseInt(copyId));
                    } catch (NumberFormatException e) {
                        throw new SQLException("Invalid Copy ID: " + copyId);
                    }
                } else {
                    // If COPIES column doesn't exist, exclude it from the update
                    sql = "UPDATE BookCopies SET Status = ?, AcquisitionDate = ?, Condition = ? WHERE CopyID = ?";
                    pstmt = conn.prepareStatement(sql);

                    pstmt.setString(1, status);

                    if (acquisitionDate != null) {
                        pstmt.setDate(2, acquisitionDate);
                    } else {
                        pstmt.setNull(2, java.sql.Types.DATE);
                    }

                    // Set condition as VARCHAR since the database column is VARCHAR
                    pstmt.setString(3, condition);

                    // Handle "NULL" string for copyId
                    try {
                        pstmt.setInt(4, Integer.parseInt(copyId));
                    } catch (NumberFormatException e) {
                        throw new SQLException("Invalid Copy ID: " + copyId);
                    }
                }
            }

            int rowsAffected = pstmt.executeUpdate();

            if (rowsAffected > 0) {
                if (isNewCopy) {
                    JOptionPane.showMessageDialog(this,
                        "New book copy created successfully!",
                        "Success",
                        JOptionPane.INFORMATION_MESSAGE);
                } else {
                    JOptionPane.showMessageDialog(this,
                        "Book copy updated successfully!",
                        "Success",
                        JOptionPane.INFORMATION_MESSAGE);
                }
                dispose(); // Close the form
            } else {
                if (isNewCopy) {
                    JOptionPane.showMessageDialog(this,
                        "Failed to create new book copy.",
                        "Error",
                        JOptionPane.ERROR_MESSAGE);
                } else {
                    JOptionPane.showMessageDialog(this,
                        "Failed to update book copy. The copy may have been deleted.",
                        "Error",
                        JOptionPane.ERROR_MESSAGE);
                }
            }

            pstmt.close();
            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "Error updating book copy: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    private void cancelButtonActionPerformed(java.awt.event.ActionEvent evt) {
        dispose(); // Close the form
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(EditBookCopiesFRM.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(EditBookCopiesFRM.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(EditBookCopiesFRM.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(EditBookCopiesFRM.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new EditBookCopiesFRM().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JTextField acquisitionDateField;
    private javax.swing.JLabel authorLabel;
    private javax.swing.JButton cancelButton;
    private javax.swing.ButtonGroup conditionGroup;
    private javax.swing.JPanel conditionPanel;
    private javax.swing.JRadioButton goodRadio;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JLabel jLabel6;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JPanel jPanel2;
    private javax.swing.JRadioButton poorRadio;
    private javax.swing.JButton saveButton;
    private javax.swing.JComboBox<String> statusComboBox;
    private javax.swing.JLabel titleLabel;
    // End of variables declaration//GEN-END:variables
}
