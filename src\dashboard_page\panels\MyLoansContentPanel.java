package dashboard_page.panels;

import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import library.management.system.database.DatabaseConnection;
import library.management.system.login_register.Login;
import dashboard_page.panels.BookCopiesContentPanel;

public class MyLoansContentPanel extends JPanel {

    // Static instance for access from other panels
    private static MyLoansContentPanel instance;

    private JTable loansTable;
    private DefaultTableModel tableModel;
    private JButton refreshButton;
    private JButton searchButton;
    private JTextField searchField;
    private JTable searchResultsTable;
    private DefaultTableModel searchResultsTableModel;
    private JDialog searchDialog;
    
    // Maximum number of books a user can borrow at one time
    private static final int MAX_BOOKS_BORROWED = 3;

    /**
     * Static method to refresh the loans data from other panels
     */
    public static void refreshData() {
        if (instance != null) {
            instance.loadLoansData();
        }
    }

    public MyLoansContentPanel() {
        initComponents();
        loadLoansData();
        instance = this;
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));
        setBackground(new Color(240, 240, 240));

        // Create header panel
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(240, 240, 240));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 10, 20));

        JLabel titleLabel = new JLabel("My Loans");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 28));
        titleLabel.setForeground(new Color(70, 70, 70));

        JLabel subtitleLabel = new JLabel("View your current and past book loans");
        subtitleLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        subtitleLabel.setForeground(new Color(120, 120, 120));

        headerPanel.add(titleLabel, BorderLayout.NORTH);
        headerPanel.add(subtitleLabel, BorderLayout.CENTER);

        // Create button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.setBackground(new Color(240, 240, 240));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(10, 20, 10, 20));

        refreshButton = new JButton("Refresh");
        refreshButton.setFont(new Font("Arial", Font.BOLD, 16));
        refreshButton.setPreferredSize(new Dimension(120, 40));
        refreshButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                loadLoansData();
            }
        });

        searchButton = new JButton("Search Books");
        searchButton.setFont(new Font("Arial", Font.BOLD, 16));
        searchButton.setPreferredSize(new Dimension(150, 40));
        searchButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                showSearchDialog();
            }
        });

        buttonPanel.add(refreshButton);
        buttonPanel.add(searchButton);

        // Create table panel
        JPanel tablePanel = new JPanel(new BorderLayout());
        tablePanel.setBackground(Color.WHITE);
        tablePanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // Create table model
        String[] columns = {"Loan ID", "Book Title", "Borrow Date", "Due Date", "Return Date", "Status"};
        tableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        loansTable = new JTable(tableModel);
        loansTable.setRowHeight(40);
        loansTable.setFont(new Font("Arial", Font.PLAIN, 14));
        loansTable.getTableHeader().setFont(new Font("Arial", Font.BOLD, 14));
        loansTable.getTableHeader().setBackground(new Color(240, 240, 240));
        loansTable.getTableHeader().setPreferredSize(new Dimension(0, 40));
        loansTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        loansTable.setAutoCreateRowSorter(true);

        // Set column widths
        loansTable.getColumnModel().getColumn(0).setPreferredWidth(80);  // Loan ID
        loansTable.getColumnModel().getColumn(1).setPreferredWidth(300); // Book Title
        loansTable.getColumnModel().getColumn(2).setPreferredWidth(120); // Borrow Date
        loansTable.getColumnModel().getColumn(3).setPreferredWidth(120); // Due Date
        loansTable.getColumnModel().getColumn(4).setPreferredWidth(120); // Return Date
        loansTable.getColumnModel().getColumn(5).setPreferredWidth(100); // Status

        // Add custom renderer for status column
        loansTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (c instanceof JLabel) {
                    JLabel label = (JLabel) c;
                    label.setBorder(BorderFactory.createEmptyBorder(0, 8, 0, 8));

                    // Reset text color to default for all cells first
                    if (!isSelected) {
                        label.setForeground(Color.BLACK);
                    }

                    // Center align Loan ID, dates, and Status columns
                    if (column == 0 || column == 2 || column == 3 || column == 4 || column == 5) {
                        label.setHorizontalAlignment(JLabel.CENTER);

                        // Color status column only
                        if (column == 5 && value != null) {
                            String status = value.toString();
                            if ("Returned".equals(status)) {
                                label.setForeground(new Color(0, 128, 0)); // Green
                            } else if ("Overdue".equals(status)) {
                                label.setForeground(new Color(220, 0, 0)); // Red
                            } else if ("Borrowed".equals(status)) {
                                label.setForeground(new Color(0, 0, 220)); // Blue
                            } else if ("Pending".equals(status)) {
                                label.setForeground(new Color(255, 165, 0)); // Orange
                            } else if ("Declined".equals(status)) {
                                label.setForeground(new Color(128, 0, 0)); // Maroon
                            }
                        }
                    } else {
                        // Left align other columns
                        label.setHorizontalAlignment(JLabel.LEFT);
                    }

                    // Apply row-level styling if needed
                    if (!isSelected) {
                        // Set alternating row background colors
                        if (row % 2 == 0) {
                            label.setBackground(new Color(240, 240, 240)); // Light gray for even rows
                        } else {
                            label.setBackground(Color.WHITE); // White for odd rows
                        }
                    }
                }

                return c;
            }
        });

        JScrollPane scrollPane = new JScrollPane(loansTable);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());

        tablePanel.add(scrollPane, BorderLayout.CENTER);

        // Create a panel to hold the header and buttons
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBackground(new Color(240, 240, 240));
        topPanel.add(headerPanel, BorderLayout.NORTH);
        topPanel.add(buttonPanel, BorderLayout.CENTER);

        // Create main panel to hold everything
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(240, 240, 240));
        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(tablePanel, BorderLayout.CENTER);

        add(mainPanel, BorderLayout.CENTER);
    }

    public void loadLoansData() {
        try {
            tableModel.setRowCount(0);

            Connection conn = DatabaseConnection.getConnection();

            // Get current user ID from the login session
            int userId = Login.getCurrentUserId();

            // Query to get all loans for the current user
            String query = "SELECT l.LoanID, b.Title, l.LoanDate, l.DueDate, l.ReturnDate, l.Status, " +
                           "CASE WHEN l.ReturnDate IS NOT NULL THEN 'Returned' " +
                           "     WHEN l.Status = 'Pending' THEN 'Pending' " +
                           "     WHEN l.Status = 'Declined' THEN 'Declined' " +
                           "     WHEN l.DueDate < CURRENT_DATE AND l.Status = 'Approved' THEN 'Overdue' " +
                           "     WHEN l.Status = 'Approved' THEN 'Borrowed' " +
                           "     ELSE l.Status END AS DisplayStatus " +
                           "FROM Loans l " +
                           "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                           "JOIN Books b ON bc.BookID = b.BookID " +
                           "WHERE l.UserID = ? " +
                           "ORDER BY l.LoanDate DESC";

            try {
                PreparedStatement stmt = conn.prepareStatement(query);
                stmt.setInt(1, userId);
                ResultSet rs = stmt.executeQuery();

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

                while (rs.next()) {
                    String loanId = rs.getString("LoanID");
                    String title = rs.getString("Title");
                    Date loanDate = rs.getDate("LoanDate");
                    Date dueDate = rs.getDate("DueDate");
                    Date returnDate = rs.getDate("ReturnDate");
                    String status = rs.getString("DisplayStatus");

                    tableModel.addRow(new Object[]{
                        loanId,
                        title,
                        loanDate != null ? dateFormat.format(loanDate) : "",
                        dueDate != null ? dateFormat.format(dueDate) : "",
                        returnDate != null ? dateFormat.format(returnDate) : "",
                        status
                    });
                }

                rs.close();
                stmt.close();
            } catch (SQLException e) {
                // If the query fails (e.g., if the Loans table doesn't exist yet), show placeholder data
                System.err.println("Error querying loans: " + e.getMessage());

                // Placeholder data
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date today = new Date();
                Date dueDate = new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000); // 14 days from today

                tableModel.addRow(new Object[]{
                    "N/A",
                    "No loans found or database error",
                    dateFormat.format(today),
                    dateFormat.format(dueDate),
                    "",
                    "N/A"
                });
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "Error loading loans: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Show dialog to search for books to borrow
     */
    private void showSearchDialog() {
        // First check if the user has reached the borrowing limit
        try {
            Connection conn = DatabaseConnection.getConnection();
            int userId = Login.getCurrentUserId();
            
            // Count how many books the user currently has borrowed (approved or overdue)
            String countQuery = "SELECT COUNT(*) as BorrowedCount FROM Loans " +
                                "WHERE UserID = ? AND Status = 'Approved' AND ReturnDate IS NULL";
            PreparedStatement countStmt = conn.prepareStatement(countQuery);
            countStmt.setInt(1, userId);
            ResultSet countRs = countStmt.executeQuery();
            
            int borrowedCount = 0;
            if (countRs.next()) {
                borrowedCount = countRs.getInt("BorrowedCount");
            }
            countRs.close();
            countStmt.close();
            
            if (borrowedCount >= MAX_BOOKS_BORROWED) {
                JOptionPane.showMessageDialog(this, 
                    "You have reached the limit of " + MAX_BOOKS_BORROWED + " borrowed books.\n" +
                    "Please return some books before borrowing more.",
                    "Borrowing Limit Reached",
                    JOptionPane.WARNING_MESSAGE);
                DatabaseConnection.closeConnection(conn);
                return;
            }
            
            DatabaseConnection.closeConnection(conn);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "Error checking borrowed books: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Create the search dialog
        searchDialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(this), "Search Books", true);
        searchDialog.setSize(800, 600);
        searchDialog.setLocationRelativeTo(this);
        searchDialog.setLayout(new BorderLayout());

        // Create search panel
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 10));
        searchField = new JTextField(30);
        JButton searchBtn = new JButton("Search");
        searchBtn.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                searchBooks();
            }
        });

        searchPanel.add(new JLabel("Enter book title or author:"));
        searchPanel.add(searchField);
        searchPanel.add(searchBtn);

        // Create table for search results
        String[] columns = {"Book ID", "Title", "Author", "Status", "Action"};
        searchResultsTableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 4; // Only the Action column is editable
            }

            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 4) {
                    return JButton.class;
                }
                return Object.class;
            }
        };

        searchResultsTable = new JTable(searchResultsTableModel);
        searchResultsTable.setRowHeight(40);
        searchResultsTable.getColumnModel().getColumn(0).setPreferredWidth(50);
        searchResultsTable.getColumnModel().getColumn(1).setPreferredWidth(300);
        searchResultsTable.getColumnModel().getColumn(2).setPreferredWidth(200);
        searchResultsTable.getColumnModel().getColumn(3).setPreferredWidth(100);
        searchResultsTable.getColumnModel().getColumn(4).setPreferredWidth(100);

        // Add button renderer and editor to the Action column
        searchResultsTable.getColumnModel().getColumn(4).setCellRenderer(new ButtonRenderer());
        searchResultsTable.getColumnModel().getColumn(4).setCellEditor(new ButtonEditor(new JCheckBox()));

        JScrollPane scrollPane = new JScrollPane(searchResultsTable);
        scrollPane.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));

        // Add components to dialog
        searchDialog.add(searchPanel, BorderLayout.NORTH);
        searchDialog.add(scrollPane, BorderLayout.CENTER);

        // Add close button at the bottom
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton closeButton = new JButton("Close");
        closeButton.addActionListener(new java.awt.event.ActionListener() {
            @Override
            public void actionPerformed(java.awt.event.ActionEvent e) {
                searchDialog.dispose();
            }
        });
        buttonPanel.add(closeButton);
        searchDialog.add(buttonPanel, BorderLayout.SOUTH);

        // Load all available books when the dialog opens
        loadAllAvailableBooks();

        // Show the dialog
        searchDialog.setVisible(true);
    }

    /**
     * Load all available books into the search results table
     */
    private void loadAllAvailableBooks() {
        try {
            searchResultsTableModel.setRowCount(0);
            Connection conn = DatabaseConnection.getConnection();

            // Get current user ID
            int userId = Login.getCurrentUserId();

            // Get books with available copies, excluding those the user has borrowed before
            String query = "SELECT b.BookID, b.Title, b.Author, " +
                          "COUNT(bc.CopyID) as AvailableCopies " +
                          "FROM Books b " +
                          "JOIN BookCopies bc ON b.BookID = bc.BookID " +
                          "WHERE bc.Status = 'Available' " +
                          // Exclude books that user has ever returned (new condition)
                          "AND NOT EXISTS (SELECT 1 FROM Loans l " +
                          "               JOIN BookCopies bc2 ON l.CopyID = bc2.CopyID " +
                          "               WHERE bc2.BookID = b.BookID " +
                          "               AND l.UserID = ? " +
                          "               AND l.ReturnDate IS NOT NULL) " +
                          "GROUP BY b.BookID, b.Title, b.Author " +
                          "ORDER BY b.Title";

            PreparedStatement stmt = conn.prepareStatement(query);
            stmt.setInt(1, userId);
            ResultSet rs = stmt.executeQuery();

            boolean foundBooks = false;
            while (rs.next()) {
                foundBooks = true;
                int bookId = rs.getInt("BookID");
                String title = rs.getString("Title");
                String author = rs.getString("Author");
                int availableCopies = rs.getInt("AvailableCopies");

                // Only show books with available copies
                if (availableCopies > 0) {
                    // Check if this book has a declined loan for the current user
                    boolean isDeclined = false;
                    String declinedQuery = "SELECT l.LoanID FROM Loans l " +
                                         "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                                         "WHERE bc.BookID = ? AND l.UserID = ? " +
                                         "AND l.Status = 'Declined' " +
                                         "AND l.ReturnDate IS NULL";

                    PreparedStatement declinedStmt = conn.prepareStatement(declinedQuery);
                    declinedStmt.setInt(1, bookId);
                    declinedStmt.setInt(2, userId);
                    ResultSet declinedRs = declinedStmt.executeQuery();

                    if (declinedRs.next()) {
                        isDeclined = true;
                    }

                    declinedRs.close();
                    declinedStmt.close();

                    String status = "Available (" + availableCopies + " copies)";
                    String buttonText = "Borrow";

                    if (isDeclined) {
                        status = "Previously Declined";
                        buttonText = "Request Again";
                    }

                    searchResultsTableModel.addRow(new Object[]{
                        bookId,
                        title,
                        author,
                        status,
                        buttonText
                    });
                }
            }

            if (!foundBooks) {
                JOptionPane.showMessageDialog(searchDialog,
                    "No available books found that you haven't borrowed before",
                    "No Books",
                    JOptionPane.INFORMATION_MESSAGE);
            }

            rs.close();
            stmt.close();
            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(searchDialog,
                "Error loading books: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Search for books based on the search term
     */
    private void searchBooks() {
        String searchTerm = searchField.getText().trim();
        if (searchTerm.isEmpty()) {
            // If search term is empty, show all available books
            loadAllAvailableBooks();
            return;
        }

        try {
            searchResultsTableModel.setRowCount(0);
            Connection conn = DatabaseConnection.getConnection();

            // Get current user ID
            int userId = Login.getCurrentUserId();

            // Search for specific books with available copies, excluding those the user has borrowed before
            String query = "SELECT b.BookID, b.Title, b.Author, COUNT(bc.CopyID) as AvailableCopies " +
                          "FROM Books b " +
                          "JOIN BookCopies bc ON b.BookID = bc.BookID " +
                          "WHERE bc.Status = 'Available' " +
                          "AND (LOWER(b.Title) LIKE ? OR LOWER(b.Author) LIKE ?) " +
                          // Exclude books that user has ever returned (new condition)
                          "AND NOT EXISTS (SELECT 1 FROM Loans l " +
                          "               JOIN BookCopies bc2 ON l.CopyID = bc2.CopyID " +
                          "               WHERE bc2.BookID = b.BookID " +
                          "               AND l.UserID = ? " +
                          "               AND l.ReturnDate IS NOT NULL) " +
                          "GROUP BY b.BookID, b.Title, b.Author " +
                          "ORDER BY b.Title";

            PreparedStatement stmt = conn.prepareStatement(query);
            stmt.setString(1, "%" + searchTerm.toLowerCase() + "%");
            stmt.setString(2, "%" + searchTerm.toLowerCase() + "%");
            stmt.setInt(3, userId);

            ResultSet rs = stmt.executeQuery();

            boolean foundBooks = false;
            while (rs.next()) {
                foundBooks = true;
                int bookId = rs.getInt("BookID");
                String title = rs.getString("Title");
                String author = rs.getString("Author");
                int availableCopies = rs.getInt("AvailableCopies");

                // Only show books with available copies
                if (availableCopies > 0) {
                    // Check if this book has a declined loan for the current user
                    boolean isDeclined = false;
                    String declinedQuery = "SELECT l.LoanID FROM Loans l " +
                                         "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                                         "WHERE bc.BookID = ? AND l.UserID = ? " +
                                         "AND l.Status = 'Declined' " +
                                         "AND l.ReturnDate IS NULL";

                    PreparedStatement declinedStmt = conn.prepareStatement(declinedQuery);
                    declinedStmt.setInt(1, bookId);
                    declinedStmt.setInt(2, userId);
                    ResultSet declinedRs = declinedStmt.executeQuery();

                    if (declinedRs.next()) {
                        isDeclined = true;
                    }

                    declinedRs.close();
                    declinedStmt.close();

                    String status = "Available (" + availableCopies + " copies)";
                    String buttonText = "Borrow";

                    if (isDeclined) {
                        status = "Previously Declined";
                        buttonText = "Request Again";
                    }

                    searchResultsTableModel.addRow(new Object[]{
                        bookId,
                        title,
                        author,
                        status,
                        buttonText
                    });
                }
            }

            if (!foundBooks) {
                JOptionPane.showMessageDialog(searchDialog,
                    "No books found matching your search",
                    "Search Results",
                    JOptionPane.INFORMATION_MESSAGE);
            }

            rs.close();
            stmt.close();
            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(searchDialog,
                "Error searching for books: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Borrow a book
     * @param bookId The ID of the book to borrow
     */
    private void borrowBook(int bookId) {
        try {
            Connection conn = DatabaseConnection.getConnection();

            // Get current user ID
            int userId = Login.getCurrentUserId();
            
            // Check if user has reached borrowing limit
            String countQuery = "SELECT COUNT(*) as BorrowedCount FROM Loans " +
                               "WHERE UserID = ? AND Status = 'Approved' AND ReturnDate IS NULL";
            PreparedStatement countStmt = conn.prepareStatement(countQuery);
            countStmt.setInt(1, userId);
            ResultSet countRs = countStmt.executeQuery();
            
            int borrowedCount = 0;
            if (countRs.next()) {
                borrowedCount = countRs.getInt("BorrowedCount");
            }
            countRs.close();
            countStmt.close();
            
            if (borrowedCount >= MAX_BOOKS_BORROWED) {
                JOptionPane.showMessageDialog(searchDialog, 
                    "You have reached the limit of " + MAX_BOOKS_BORROWED + " borrowed books.\n" +
                    "Please return some books before borrowing more.",
                    "Borrowing Limit Reached",
                    JOptionPane.WARNING_MESSAGE);
                DatabaseConnection.closeConnection(conn);
                return;
            }

            // First, check if the user already has a pending or declined loan for this book
            String existingLoanQuery = "SELECT l.LoanID, l.Status FROM Loans l " +
                                      "JOIN BookCopies bc ON l.CopyID = bc.CopyID " +
                                      "WHERE bc.BookID = ? AND l.UserID = ? " +
                                      "AND (l.Status = 'Pending' OR l.Status = 'Declined') " +
                                      "AND l.ReturnDate IS NULL";
            PreparedStatement existingLoanStmt = conn.prepareStatement(existingLoanQuery);
            existingLoanStmt.setInt(1, bookId);
            existingLoanStmt.setInt(2, userId);
            ResultSet existingLoanRs = existingLoanStmt.executeQuery();

            if (existingLoanRs.next()) {
                String status = existingLoanRs.getString("Status");
                if ("Pending".equals(status)) {
                    JOptionPane.showMessageDialog(searchDialog,
                        "You already have a pending loan request for this book",
                        "Duplicate Request",
                        JOptionPane.WARNING_MESSAGE);
                    existingLoanRs.close();
                    existingLoanStmt.close();
                    DatabaseConnection.closeConnection(conn);
                    return;
                } else if ("Declined".equals(status)) {
                    // For declined loans, check if the button text is "Request Again"
                    // If it is, we'll allow the user to request the book again
                    // This is handled by the table renderer showing "Request Again" for declined books

                    // Get the loan ID to update it
                    int loanId = existingLoanRs.getInt("LoanID");

                    // Ask for confirmation to request again
                    int confirmReRequest = JOptionPane.showConfirmDialog(searchDialog,
                        "Your previous request for this book was declined. Would you like to request it again?",
                        "Request Again",
                        JOptionPane.YES_NO_OPTION);

                    if (confirmReRequest != JOptionPane.YES_OPTION) {
                        existingLoanRs.close();
                        existingLoanStmt.close();
                        DatabaseConnection.closeConnection(conn);
                        return;
                    }

                    // Update the existing loan record to Pending status
                    String updateQuery = "UPDATE Loans SET Status = 'Pending', ReturnDate = NULL WHERE LoanID = ?";
                    PreparedStatement updateStmt = conn.prepareStatement(updateQuery);
                    updateStmt.setInt(1, loanId);
                    int rowsUpdated = updateStmt.executeUpdate();
                    updateStmt.close();

                    if (rowsUpdated > 0) {
                        JOptionPane.showMessageDialog(searchDialog,
                            "Your loan request has been resubmitted and is pending approval.",
                            "Request Resubmitted",
                            JOptionPane.INFORMATION_MESSAGE);

                        // Refresh the loans table
                        loadLoansData();

                        // Close the search dialog
                        searchDialog.dispose();
                    } else {
                        JOptionPane.showMessageDialog(searchDialog,
                            "Failed to resubmit your loan request. Please try again.",
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                    }

                    existingLoanRs.close();
                    existingLoanStmt.close();
                    DatabaseConnection.closeConnection(conn);
                    return;
                }
            }
            existingLoanRs.close();
            existingLoanStmt.close();

            // Then, check if there are available copies
            String checkQuery = "SELECT CopyID, Status FROM BookCopies WHERE BookID = ? AND Status = 'Available' FETCH FIRST 1 ROWS ONLY";
            PreparedStatement checkStmt = conn.prepareStatement(checkQuery);
            checkStmt.setInt(1, bookId);
            ResultSet checkRs = checkStmt.executeQuery();

            if (!checkRs.next()) {
                JOptionPane.showMessageDialog(searchDialog,
                    "No available copies of this book",
                    "Borrow Error",
                    JOptionPane.ERROR_MESSAGE);
                checkRs.close();
                checkStmt.close();
                DatabaseConnection.closeConnection(conn);
                return;
            }

            int copyId = checkRs.getInt("CopyID");
            checkRs.close();
            checkStmt.close();

            // Get book title for confirmation
            String titleQuery = "SELECT Title FROM Books WHERE BookID = ?";
            PreparedStatement titleStmt = conn.prepareStatement(titleQuery);
            titleStmt.setInt(1, bookId);
            ResultSet titleRs = titleStmt.executeQuery();

            String bookTitle = "this book";
            if (titleRs.next()) {
                bookTitle = titleRs.getString("Title");
            }
            titleRs.close();
            titleStmt.close();

            // Create a confirmation dialog (simpler now, without date selection)
            int confirm = JOptionPane.showConfirmDialog(searchDialog,
                "Do you want to borrow \"" + bookTitle + "\"?\n\n" +
                "The due date will be automatically set to 7 days from today.",
                "Confirm Borrowing",
                JOptionPane.YES_NO_OPTION);
                
            if (confirm != JOptionPane.YES_OPTION) {
                DatabaseConnection.closeConnection(conn);
                return;
            }

            // Calculate due date as 7 days from today
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, 7);
            java.sql.Date dueDate = new java.sql.Date(calendar.getTimeInMillis());

            // Get a librarian ID (for now, use ID 1 which should be the admin)
            int librarianId = 1;

            // Insert the loan record with 'Pending' status
            String insertQuery = "INSERT INTO Loans (CopyID, UserID, IssuedByUserID, LoanDate, DueDate, Status) VALUES (?, ?, ?, CURRENT_DATE, ?, 'Pending')";
            PreparedStatement insertStmt = conn.prepareStatement(insertQuery);
            insertStmt.setInt(1, copyId);
            insertStmt.setInt(2, userId);
            insertStmt.setInt(3, librarianId);
            insertStmt.setDate(4, dueDate);

            int rowsAffected = insertStmt.executeUpdate();
            insertStmt.close();

            if (rowsAffected > 0) {
                JOptionPane.showMessageDialog(searchDialog,
                    "Book loan request submitted successfully!\n\nTitle: " + bookTitle +
                    "\nDue Date: " + dueDate + 
                    "\n\nYour request is pending approval by a librarian.",
                    "Request Submitted",
                    JOptionPane.INFORMATION_MESSAGE);

                // Refresh the loans table
                loadLoansData();

                // Close the search dialog
                searchDialog.dispose();
            } else {
                JOptionPane.showMessageDialog(searchDialog,
                    "Failed to borrow book",
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            JOptionPane.showMessageDialog(searchDialog,
                "Error borrowing book: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Button renderer for the action column in the search results table
     */
    private class ButtonRenderer extends JButton implements TableCellRenderer {
        public ButtonRenderer() {
            setOpaque(true);
        }

        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
            setText((value == null) ? "" : value.toString());
            return this;
        }
    }

    /**
     * Button editor for the action column in the search results table
     */
    private class ButtonEditor extends DefaultCellEditor {
        private JButton button;
        private String label;
        private boolean isPushed;
        private int bookId;

        public ButtonEditor(JCheckBox checkBox) {
            super(checkBox);
            button = new JButton();
            button.setOpaque(true);
            button.addActionListener(new java.awt.event.ActionListener() {
                @Override
                public void actionPerformed(java.awt.event.ActionEvent e) {
                    fireEditingStopped();
                }
            });
        }

        @Override
        public Component getTableCellEditorComponent(JTable table, Object value, boolean isSelected, int row, int column) {
            if (isSelected) {
                button.setForeground(table.getSelectionForeground());
                button.setBackground(table.getSelectionBackground());
            } else {
                button.setForeground(table.getForeground());
                button.setBackground(UIManager.getColor("Button.background"));
            }

            label = (value == null) ? "" : value.toString();
            button.setText(label);
            isPushed = true;

            // Get the book ID from the first column
            bookId = Integer.parseInt(table.getValueAt(row, 0).toString());

            return button;
        }

        @Override
        public Object getCellEditorValue() {
            if (isPushed) {
                // Handle button click
                if ("Borrow".equals(label) || "Request Again".equals(label)) {
                    borrowBook(bookId);
                }
            }
            isPushed = false;
            return label;
        }

        @Override
        public boolean stopCellEditing() {
            isPushed = false;
            return super.stopCellEditing();
        }
    }
}
