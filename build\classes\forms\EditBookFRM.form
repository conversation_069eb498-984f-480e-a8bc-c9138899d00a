<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="jPanel1" max="32767" attributes="0"/>
          <Component id="jPanel2" alignment="0" max="32767" attributes="0"/>
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace max="32767" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel6" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel7" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel2" alignment="0" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="isbn" min="-2" pref="317" max="-2" attributes="0"/>
                  <Component id="title" min="-2" pref="317" max="-2" attributes="0"/>
                  <Component id="publisher" min="-2" pref="317" max="-2" attributes="0"/>
                  <Component id="edition" min="-2" pref="317" max="-2" attributes="0"/>
                  <Component id="genre" min="-2" pref="317" max="-2" attributes="0"/>
                  <Component id="publication_year" min="-2" pref="317" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="35" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="40" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="isbn" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="25" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="title" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="25" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="publisher" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="25" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="publication_year" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="25" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel6" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="genre" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="25" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel7" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="edition" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace pref="40" max="32767" attributes="0"/>
              <Component id="jPanel2" min="-2" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="cc" green="cc" red="cc" type="rgb"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="202" max="-2" attributes="0"/>
                  <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="30" max="32767" attributes="0"/>
                  <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="27" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="jLabel1">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="24" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="EDIT BOOK"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="jPanel2">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="cc" green="cc" red="cc" type="rgb"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace min="-2" pref="59" max="-2" attributes="0"/>
                  <Component id="closeBTN" min="-2" pref="197" max="-2" attributes="0"/>
                  <EmptySpace pref="37" max="32767" attributes="0"/>
                  <Component id="saveChangesBTN" min="-2" pref="197" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="48" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="30" max="32767" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="closeBTN" alignment="3" min="-2" pref="36" max="-2" attributes="0"/>
                      <Component id="saveChangesBTN" alignment="3" min="-2" pref="36" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JButton" name="closeBTN">
          <Properties>
            <Property name="text" type="java.lang.String" value="CLOSE"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JButton" name="saveChangesBTN">
          <Properties>
            <Property name="text" type="java.lang.String" value="SAVE CHANGES"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="ISBN"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="isbn">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="Title"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="title">
    </Component>
    <Component class="javax.swing.JTextField" name="publisher">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="text" type="java.lang.String" value="Publisher"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="publication_year">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="text" type="java.lang.String" value="Publication Year"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="genre">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel6">
      <Properties>
        <Property name="text" type="java.lang.String" value="Genre"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="edition">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel7">
      <Properties>
        <Property name="text" type="java.lang.String" value="Edition"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
