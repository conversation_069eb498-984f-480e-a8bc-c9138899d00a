package dashboard_page.panels;

import javax.swing.*;
import javax.swing.border.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import library.management.system.database.DatabaseConnection;


public class UserDashboardContentPanel extends JPanel {


    // Store references to value labels for updating
    private JLabel borrowedBooksValueLabel;
    private JLabel overdueValueLabel;
    private JLabel finesValueLabel;
    private JLabel historyValueLabel;

    public UserDashboardContentPanel() {
        initComponents();
        loadDashboardData();
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));

        setBackground(new Color(240, 240, 240));

        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(240, 240, 240));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(20, 50, 10, 50));

        JLabel titleLabel = new JLabel("User Dashboard");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 28));
        titleLabel.setForeground(new Color(70, 70, 70));

        JLabel subtitleLabel = new JLabel("Welcome to your library account");
        subtitleLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        subtitleLabel.setForeground(new Color(120, 120, 120));

        JPanel titlePanel = new JPanel();
        titlePanel.setLayout(new BoxLayout(titlePanel, BoxLayout.Y_AXIS));
        titlePanel.setBackground(new Color(240, 240, 240));
        titlePanel.add(titleLabel);
        titlePanel.add(Box.createRigidArea(new Dimension(0, 5)));
        titlePanel.add(subtitleLabel);

        headerPanel.add(titlePanel, BorderLayout.WEST);

        JPanel contentPanel = new JPanel(new GridLayout(2, 2, 30, 30));
        contentPanel.setBorder(BorderFactory.createEmptyBorder(20, 50, 50, 50));
        contentPanel.setBackground(new Color(240, 240, 240));

        // Create panels with initial values (will be updated by loadDashboardData)
        JPanel borrowedPanel = createStatPanel("Books Borrowed", "0", new Color(100, 181, 246));
        borrowedBooksValueLabel = findValueLabel(borrowedPanel);
        contentPanel.add(borrowedPanel);

        JPanel overduePanel = createStatPanel("Books Overdue", "0", new Color(239, 154, 154));
        overdueValueLabel = findValueLabel(overduePanel);
        contentPanel.add(overduePanel);

        JPanel finesPanel = createStatPanel("Fines Due", "₱0.00", new Color(255, 183, 77));
        finesValueLabel = findValueLabel(finesPanel);
        contentPanel.add(finesPanel);

        JPanel historyPanel = createStatPanel("Reading History", "0 books", new Color(174, 213, 129));
        historyValueLabel = findValueLabel(historyPanel);
        contentPanel.add(historyPanel);

        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(240, 240, 240));
        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(contentPanel, BorderLayout.CENTER);

        add(mainPanel, BorderLayout.CENTER);
    }

    private JPanel createStatPanel(String title, String value, Color color) {
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(0, 0, 0, 30), 1),
            BorderFactory.createEmptyBorder(0, 0, 0, 0)
        ));

        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBackground(color);
        panel.setBorder(BorderFactory.createEmptyBorder(25, 25, 25, 25));

        panel.setPreferredSize(new Dimension(250, 180));

        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 22));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        panel.add(Box.createVerticalGlue());
        panel.add(titleLabel);
        panel.add(Box.createRigidArea(new Dimension(0, 15)));

        JLabel valueLabel = new JLabel(value);
        valueLabel.setFont(new Font("Arial", Font.BOLD, 48));
        valueLabel.setForeground(Color.WHITE);
        valueLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        panel.add(valueLabel);
        panel.add(Box.createVerticalGlue());

        mainPanel.add(panel, BorderLayout.CENTER);

        mainPanel.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                panel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));
                panel.setBackground(color.brighter());
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                panel.setBorder(BorderFactory.createEmptyBorder(25, 25, 25, 25));
                panel.setBackground(color);
            }
        });

        return mainPanel;
    }

    /**
     * Helper method to find the value label in a stat panel
     */
    private JLabel findValueLabel(JPanel panel) {
        // The stat panel structure is:
        // mainPanel -> panel (BoxLayout) -> valueLabel (second JLabel)
        JPanel innerPanel = (JPanel) panel.getComponent(0);
        // The value label is the 4th component (index 3) in the inner panel
        return (JLabel) innerPanel.getComponent(3);
    }

    /**
     * Load real data from the database for the dashboard
     * Public method to allow refreshing from outside
     */
    public void loadDashboardData() {
        try {
            Connection conn = DatabaseConnection.getConnection();

            // Get current user ID from the session
            int userId = library.management.system.login_register.Login.getCurrentUserId();

            try {
                // Get borrowed books count
                String borrowedQuery = "SELECT COUNT(*) FROM Loans WHERE UserID = ? AND ReturnDate IS NULL";
                PreparedStatement borrowedStmt = conn.prepareStatement(borrowedQuery);
                borrowedStmt.setInt(1, userId);
                ResultSet borrowedRs = borrowedStmt.executeQuery();
                if (borrowedRs.next()) {
                    int borrowedCount = borrowedRs.getInt(1);
                    borrowedBooksValueLabel.setText(String.valueOf(borrowedCount));
                }
                borrowedRs.close();
                borrowedStmt.close();
            } catch (Exception e) {
                System.err.println("Error getting borrowed books count: " + e.getMessage());
                // Use placeholder data
                borrowedBooksValueLabel.setText("0");
            }

            try {
                // Get overdue books count
                String overdueQuery = "SELECT COUNT(*) FROM Loans WHERE UserID = ? AND ReturnDate IS NULL AND DueDate < CURRENT_DATE";
                PreparedStatement overdueStmt = conn.prepareStatement(overdueQuery);
                overdueStmt.setInt(1, userId);
                ResultSet overdueRs = overdueStmt.executeQuery();
                if (overdueRs.next()) {
                    int overdueCount = overdueRs.getInt(1);
                    overdueValueLabel.setText(String.valueOf(overdueCount));
                }
                overdueRs.close();
                overdueStmt.close();
            } catch (Exception e) {
                System.err.println("Error getting overdue books count: " + e.getMessage());
                // Use placeholder data
                overdueValueLabel.setText("0");
            }

            try {
                // Get fines due for overdue books only
                String finesQuery = "SELECT SUM(f.Amount) FROM Fines f " +
                                   "JOIN Loans l ON f.LoanID = l.LoanID " +
                                   "WHERE f.UserID = ? AND f.Status = 'Unpaid' " +
                                   "AND l.DueDate < CURRENT_DATE";
                PreparedStatement finesStmt = conn.prepareStatement(finesQuery);
                finesStmt.setInt(1, userId);
                ResultSet finesRs = finesStmt.executeQuery();

                double totalFines = 0.0;
                if (finesRs.next()) {
                    totalFines = finesRs.getDouble(1);
                    // Handle null result (no fines)
                    if (finesRs.wasNull()) {
                        totalFines = 0.0;
                    }
                }
                finesValueLabel.setText(String.format("₱%.2f", totalFines));

                finesRs.close();
                finesStmt.close();
            } catch (Exception e) {
                System.err.println("Error getting fines: " + e.getMessage());
                // Use placeholder data
                finesValueLabel.setText("₱0.00");
            }

            try {
                // Get reading history count
                String historyQuery = "SELECT COUNT(*) FROM Loans WHERE UserID = ? AND ReturnDate IS NOT NULL";
                PreparedStatement historyStmt = conn.prepareStatement(historyQuery);
                historyStmt.setInt(1, userId);
                ResultSet historyRs = historyStmt.executeQuery();
                if (historyRs.next()) {
                    int historyCount = historyRs.getInt(1);
                    historyValueLabel.setText(historyCount + " books");
                }
                historyRs.close();
                historyStmt.close();
            } catch (Exception e) {
                System.err.println("Error getting reading history: " + e.getMessage());
                // Use placeholder data
                historyValueLabel.setText("0 books");
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            System.err.println("Error loading dashboard data: " + e.getMessage());
            e.printStackTrace();

            // Set placeholder data if there's an error
            borrowedBooksValueLabel.setText("0");
            overdueValueLabel.setText("0");
            finesValueLabel.setText("₱0.00");
            historyValueLabel.setText("0 books");
        }
    }
}
