package library.management.system.database;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class DatabaseChecker {
    
    public static void main(String[] args) {
        try {
            System.out.println("Checking database structure...");
            Connection conn = DatabaseConnection.getConnection();
            checkTableStructure(conn, "BOOKCOPIES");
            
            // Try to fix the Condition column if it's BOOLEAN
            fixConditionColumn(conn);
            
            DatabaseConnection.closeConnection(conn);
        } catch (Exception e) {
            System.err.println("Error checking database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void checkTableStructure(Connection conn, String tableName) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet columns = metaData.getColumns(null, "APP", tableName, null);
        
        System.out.println("Columns in " + tableName + " table:");
        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
            String dataType = columns.getString("TYPE_NAME");
            int columnSize = columns.getInt("COLUMN_SIZE");
            String isNullable = columns.getString("IS_NULLABLE");
            String defaultValue = columns.getString("COLUMN_DEF");
            
            System.out.println(columnName + " - " + dataType + 
                               " (Size: " + columnSize + ", Nullable: " + isNullable + 
                               ", Default: " + defaultValue + ")");
        }
        columns.close();
    }
    
    public static void fixConditionColumn(Connection conn) {
        try {
            // First check if the Condition column is BOOLEAN
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet columns = metaData.getColumns(null, "APP", "BOOKCOPIES", "CONDITION");
            
            boolean isBoolean = false;
            if (columns.next()) {
                String dataType = columns.getString("TYPE_NAME");
                System.out.println("Condition column data type: " + dataType);
                isBoolean = "BOOLEAN".equalsIgnoreCase(dataType);
            }
            columns.close();
            
            if (isBoolean) {
                System.out.println("Condition column is BOOLEAN, attempting to fix...");
                
                // Create a temporary column
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute("ALTER TABLE APP.BOOKCOPIES ADD COLUMN CONDITION_NEW VARCHAR(255)");
                    System.out.println("Added temporary CONDITION_NEW column");
                    
                    // Copy data from CONDITION to CONDITION_NEW, converting boolean to string
                    stmt.execute("UPDATE APP.BOOKCOPIES SET CONDITION_NEW = CASE WHEN CONDITION = TRUE THEN 'Good' ELSE 'Poor' END");
                    System.out.println("Copied and converted data to CONDITION_NEW");
                    
                    // Drop the original CONDITION column
                    stmt.execute("ALTER TABLE APP.BOOKCOPIES DROP COLUMN CONDITION");
                    System.out.println("Dropped original CONDITION column");
                    
                    // Rename CONDITION_NEW to CONDITION
                    stmt.execute("RENAME COLUMN APP.BOOKCOPIES.CONDITION_NEW TO CONDITION");
                    System.out.println("Renamed CONDITION_NEW to CONDITION");
                    
                    System.out.println("Successfully fixed CONDITION column!");
                } catch (SQLException e) {
                    System.err.println("Error fixing CONDITION column: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                System.out.println("Condition column is not BOOLEAN, no fix needed");
            }
        } catch (SQLException e) {
            System.err.println("Error checking CONDITION column: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
