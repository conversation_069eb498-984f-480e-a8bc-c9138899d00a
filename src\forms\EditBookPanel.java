package forms;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement; // Add this missing import
import java.util.Calendar;
import java.util.Properties;
import java.util.Date;
import javax.swing.*;
import java.awt.*;
import org.jdatepicker.impl.JDatePanelImpl;
import org.jdatepicker.impl.JDatePickerImpl;
import org.jdatepicker.impl.UtilDateModel;
import library.management.system.database.DatabaseConnection;

/**
 * A panel for editing existing books in the database.
 * This can be embedded into other containers.
 */
public class EditBookPanel extends JPanel {
    // Callback interface for notifying parent when a book is edited
    public interface BookEditedListener {
        void onBookEdited();
    }
    
    private BookEditedListener listener;
    private int bookId;
    
    // Form components
    private JTextField isbnField;
    private JTextField titleField;
    private JTextField authorField;
    private JTextField publisher<PERSON>ield;
    private JTextField publicationYearField;
    private JTextField copyrightField;
    private JTextField genreField;
    private JTextField editionField;
    private JTextField copiesField;
    private JComboBox<String> statusComboBox;
    private JDatePickerImpl datePicker; // Changed from combo boxes to JDatePicker
    private JComboBox<String> conditionComboBox; // Changed from JTextField to JComboBox
    private JButton saveButton;
    private JButton cancelButton;
    
    /**
     * Creates a new EditBookPanel for the specified book
     * @param bookId The ID of the book to edit
     * @param listener Callback for when a book is successfully edited
     */
    public EditBookPanel(int bookId, BookEditedListener listener) {
        this.bookId = bookId;
        this.listener = listener;
        initComponents();
        loadBookData();
    }
    
    /**
     * Creates a new EditBookPanel for the specified book without callback
     * @param bookId The ID of the book to edit
     */
    public EditBookPanel(int bookId) {
        this(bookId, null);
    }

    private void initComponents() {
        // Simple panel layout
        setLayout(new BorderLayout(10, 10));
        
        // Create header with simple title
        JPanel headerPanel = new JPanel();
        JLabel titleLabel = new JLabel("Edit Book");
        titleLabel.setFont(new Font(titleLabel.getFont().getName(), Font.BOLD, 16));
        headerPanel.add(titleLabel);
        
        // Main form panel using a simple layout
        JPanel formPanel = new JPanel();
        formPanel.setLayout(new BoxLayout(formPanel, BoxLayout.Y_AXIS));
        formPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        
        // Basic information panel
        JPanel basicInfoPanel = new JPanel(new GridLayout(0, 2, 5, 5));
        basicInfoPanel.setBorder(BorderFactory.createTitledBorder("Basic Information"));
        
        // Add form fields
        basicInfoPanel.add(new JLabel("ISBN:"));
        isbnField = new JTextField(20);
        basicInfoPanel.add(isbnField);
        
        basicInfoPanel.add(new JLabel("Title:"));
        titleField = new JTextField(20);
        basicInfoPanel.add(titleField);
        
        basicInfoPanel.add(new JLabel("Author:"));
        authorField = new JTextField(20);
        basicInfoPanel.add(authorField);
        
        // Publishing information panel
        JPanel publishingInfoPanel = new JPanel(new GridLayout(0, 2, 5, 5));
        publishingInfoPanel.setBorder(BorderFactory.createTitledBorder("Publishing Information"));
        
        publishingInfoPanel.add(new JLabel("Publisher:"));
        publisherField = new JTextField(20);
        publishingInfoPanel.add(publisherField);
        
        publishingInfoPanel.add(new JLabel("Publication Year:"));
        publicationYearField = new JTextField(20);
        publishingInfoPanel.add(publicationYearField);
        
        publishingInfoPanel.add(new JLabel("Copyright:"));
        copyrightField = new JTextField(20);
        publishingInfoPanel.add(copyrightField);
        
        publishingInfoPanel.add(new JLabel("Genre:"));
        genreField = new JTextField(20);
        publishingInfoPanel.add(genreField);
        
        publishingInfoPanel.add(new JLabel("Edition:"));
        editionField = new JTextField(20);
        publishingInfoPanel.add(editionField);
        
        // Catalog information panel
        JPanel catalogInfoPanel = new JPanel(new GridLayout(0, 2, 5, 5));
        catalogInfoPanel.setBorder(BorderFactory.createTitledBorder("Catalog Information"));
        
        catalogInfoPanel.add(new JLabel("Copies:"));
        copiesField = new JTextField();
        catalogInfoPanel.add(copiesField);
        
        catalogInfoPanel.add(new JLabel("Status:"));
        statusComboBox = new JComboBox<>(new String[] {
            "Available", "Loaned", "Reserved", "Under Repair", "Lost"
        });
        catalogInfoPanel.add(statusComboBox);
        
        catalogInfoPanel.add(new JLabel("Acquisition Date:"));
        
        // Set up JDatePicker
        UtilDateModel model = new UtilDateModel();
        Properties p = new Properties();
        p.put("text.today", "Today");
        p.put("text.month", "Month");
        p.put("text.year", "Year");
        JDatePanelImpl datePanel = new JDatePanelImpl(model, p);
        datePicker = new JDatePickerImpl(datePanel, new DateLabelFormatter());
        catalogInfoPanel.add(datePicker);
        
        catalogInfoPanel.add(new JLabel("Condition:"));
        conditionComboBox = new JComboBox<>(new String[] {
            "Good", "Acceptable", "Fair", "Poor"
        });
        catalogInfoPanel.add(conditionComboBox);
        
        // Add panels to the form panel with some spacing
        formPanel.add(basicInfoPanel);
        formPanel.add(Box.createVerticalStrut(10));
        formPanel.add(publishingInfoPanel);
        formPanel.add(Box.createVerticalStrut(10));
        formPanel.add(catalogInfoPanel);
        
        // Button panel with simple layout
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        
        cancelButton = new JButton("Cancel");
        cancelButton.addActionListener(e -> {
            // Find the parent window and close it
            Window window = SwingUtilities.getWindowAncestor(this);
            if (window != null) {
                window.dispose();
            }
        });
        
        saveButton = new JButton("Save Changes");
        saveButton.addActionListener(e -> updateBook());
        
        buttonPanel.add(cancelButton);
        buttonPanel.add(saveButton);
        
        // Add all panels to the main panel - no scroll pane
        add(headerPanel, BorderLayout.NORTH);
        add(formPanel, BorderLayout.CENTER);
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    private void loadBookData() {
        try {
            Connection conn = DatabaseConnection.getConnection();
            String sql = "SELECT * FROM Books WHERE BookID = ?";
            PreparedStatement pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, bookId);
            
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                isbnField.setText(rs.getString("ISBN"));
                titleField.setText(rs.getString("Title"));
                authorField.setText(rs.getString("Author"));
                publisherField.setText(rs.getString("Publisher"));
                
                // Handle publication year
                Object pubYearObj = rs.getObject("PublicationYear");
                publicationYearField.setText(pubYearObj != null ? String.valueOf(rs.getInt("PublicationYear")) : "");
                
                // Handle copyright
                String copyright = rs.getString("Copyright");
                copyrightField.setText(copyright != null ? copyright : "");
                
                // Handle genre
                String genre = rs.getString("Genre");
                genreField.setText(genre != null ? genre : "");
                
                // Handle edition
                String edition = rs.getString("Edition");
                editionField.setText(edition != null ? edition : "");
                
                // Handle copies - safely check if column exists
                try {
                    int copies = rs.getInt("Copies");
                    copiesField.setText(String.valueOf(copies));
                } catch (SQLException e) {
                    // Column doesn't exist or other error
                    System.out.println("Note: Copies column not found, using default value");
                    copiesField.setText("0");
                }
                
                // Handle status
                String status = rs.getString("Status");
                if (status != null) {
                    for (int i = 0; i < statusComboBox.getItemCount(); i++) {
                        if (statusComboBox.getItemAt(i).equals(status)) {
                            statusComboBox.setSelectedIndex(i);
                            break;
                        }
                    }
                }
                
                // Handle acquisition date
                java.sql.Date acquisitionDate = rs.getDate("AcquisitionDate");
                if (acquisitionDate != null) {
                    // Convert SQL Date to util Date and set in the date picker
                    Date utilDate = new Date(acquisitionDate.getTime());
                    ((UtilDateModel)datePicker.getModel()).setValue(utilDate);
                }
                
                // Handle condition
                String condition = rs.getString("Condition");
                if (condition != null && !condition.isEmpty()) {
                    // Try to match it with the combo box items
                    for (int i = 0; i < conditionComboBox.getItemCount(); i++) {
                        if (conditionComboBox.getItemAt(i).equalsIgnoreCase(condition)) {
                            conditionComboBox.setSelectedIndex(i);
                            break;
                        }
                    }
                } else {
                    conditionComboBox.setSelectedIndex(0); // Default to "Good"
                }
            } else {
                JOptionPane.showMessageDialog(this, 
                    "Book with ID " + bookId + " not found.",
                    "Error", 
                    JOptionPane.ERROR_MESSAGE);
                
                // Close the window
                Window window = SwingUtilities.getWindowAncestor(this);
                if (window != null) {
                    window.dispose();
                }
            }
            
            rs.close();
            pstmt.close();
            DatabaseConnection.closeConnection(conn);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "Error loading book data: " + e.getMessage(),
                "Database Error", 
                JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }
    
    private void updateBook() {
        // Get values from form fields
        String isbnValue = isbnField.getText().trim();
        String titleValue = titleField.getText().trim();
        String authorValue = authorField.getText().trim();
        String publisherValue = publisherField.getText().trim();
        String pubYearValue = publicationYearField.getText().trim();
        String copyrightValue = copyrightField.getText().trim();
        String genreValue = genreField.getText().trim();
        String editionValue = editionField.getText().trim();
        String copiesValue = copiesField.getText().trim();
        String statusValue = statusComboBox.getSelectedItem().toString();
        String conditionValue = conditionComboBox.getSelectedItem().toString();
        
        // Handle acquisition date from JDatePicker
        java.sql.Date acquisitionDate = null;
        if (datePicker.getModel().getValue() != null) {
            Date selectedDate = (Date) datePicker.getModel().getValue();
            acquisitionDate = new java.sql.Date(selectedDate.getTime());
        }
        
        // Validate required fields
        if (isbnValue.isEmpty() || titleValue.isEmpty() || authorValue.isEmpty()) {
            JOptionPane.showMessageDialog(this, 
                "ISBN, Title, and Author are required fields.", 
                "Validation Error", 
                JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // Check if ISBN changed and is unique
        if (!isbnValue.equals(getOriginalISBN()) && !isIsbnUnique(isbnValue)) {
            JOptionPane.showMessageDialog(this, 
                "ISBN is already used by another book.", 
                "Validation Error", 
                JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            // Get connection using the proper method from DatabaseConnection
            conn = DatabaseConnection.getConnection();
            
            // Simple update SQL matching the Books table schema
            String sql = "UPDATE Books SET " +
                        "ISBN = ?, " +
                        "Title = ?, " + 
                        "Author = ?, " +
                        "Publisher = ?, " +
                        "PublicationYear = ?, " +
                        "Copyright = ?, " +
                        "Genre = ?, " +
                        "Edition = ?, " +
                        "Copies = ?, " +
                        "Status = ?, " +
                        "AcquisitionDate = ?, " +
                        "Condition = ? " +
                        "WHERE BookID = ?";
            
            pstmt = conn.prepareStatement(sql);
            
            // Set parameters
            int i = 1;
            pstmt.setString(i++, isbnValue);
            pstmt.setString(i++, titleValue);
            pstmt.setString(i++, authorValue);
            pstmt.setString(i++, publisherValue);
            
            // Handle publication year (integer)
            if (!pubYearValue.isEmpty()) {
                try {
                    pstmt.setInt(i++, Integer.parseInt(pubYearValue));
                } catch (NumberFormatException e) {
                    pstmt.setNull(i++, java.sql.Types.INTEGER);
                }
            } else {
                pstmt.setNull(i++, java.sql.Types.INTEGER);
            }
            
            pstmt.setString(i++, copyrightValue);
            pstmt.setString(i++, genreValue);
            pstmt.setString(i++, editionValue);
            
            // Handle copies (integer)
            int copies = 0;
            if (!copiesValue.isEmpty()) {
                try {
                    copies = Integer.parseInt(copiesValue);
                } catch (NumberFormatException e) {
                    // Default to 0
                }
            }
            pstmt.setInt(i++, copies);
            
            pstmt.setString(i++, statusValue);
            
            // Handle acquisition date
            if (acquisitionDate != null) {
                pstmt.setDate(i++, acquisitionDate);
            } else {
                pstmt.setNull(i++, java.sql.Types.DATE);
            }
            
            pstmt.setString(i++, conditionValue);
            
            // Set the BookID for the WHERE clause
            pstmt.setInt(i++, bookId);
            
            // Execute update
            int rowsAffected = pstmt.executeUpdate();
            
            if (rowsAffected > 0) {
                JOptionPane.showMessageDialog(this,
                    "Book updated successfully!",
                    "Success",
                    JOptionPane.INFORMATION_MESSAGE);
                
                // Notify listener if available
                if (listener != null) {
                    listener.onBookEdited();
                }
                
                // Close the window
                Window window = SwingUtilities.getWindowAncestor(this);
                if (window != null) {
                    window.dispose();
                }
            } else {
                JOptionPane.showMessageDialog(this,
                    "No changes were made. The book may no longer exist.",
                    "Warning",
                    JOptionPane.WARNING_MESSAGE);
            }
        } catch (SQLException e) {
            String message = "Database error: " + e.getMessage();
            
            // Special handling for unique constraint violation
            if (e.getSQLState() != null && e.getSQLState().equals("23505")) {
                message = "ISBN is already used by another book.";
            }
            
            JOptionPane.showMessageDialog(this, message, "Error", JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "Error: " + e.getMessage(), 
                "Error", 
                JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        } finally {
            // Proper resource cleanup using DatabaseConnection methods
            if (pstmt != null) {
                try { pstmt.close(); } catch (SQLException e) { /* ignore */ }
            }
            DatabaseConnection.closeConnection(conn);
        }
    }
    
    /**
     * Checks if the ISBN is unique in the database
     * @param isbn The ISBN to check
     * @return true if ISBN is unique or is this book's original ISBN
     */
    private boolean isIsbnUnique(String isbn) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(
                "SELECT COUNT(*) FROM Books WHERE ISBN = ? AND BookID <> ?")) {
            
            stmt.setString(1, isbn);
            stmt.setInt(2, bookId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) == 0; // Return true if count is 0 (unique)
                }
            }
        } catch (Exception e) {
            System.err.println("Error checking ISBN uniqueness: " + e.getMessage());
        }
        
        return false; // Default to false on error
    }
    
    /**
     * Set a listener to be notified when a book is successfully edited
     * @param listener The listener to set
     */
    public void setBookEditedListener(BookEditedListener listener) {
        this.listener = listener;
    }
    
    // Keep the getOriginalISBN method
    private String getOriginalISBN() {
        String isbn = "";
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            PreparedStatement stmt = conn.prepareStatement("SELECT ISBN FROM Books WHERE BookID = ?");
            stmt.setInt(1, bookId);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                isbn = rs.getString("ISBN");
            }
            
            rs.close();
            stmt.close();
            DatabaseConnection.closeConnection(conn);
        } catch (Exception e) {
            System.err.println("Error getting original ISBN: " + e.getMessage());
        }
        
        return isbn;
    }
}
