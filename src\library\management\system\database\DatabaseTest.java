package library.management.system.database;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import javax.swing.JOptionPane;

public class DatabaseTest {

    public static boolean testConnection() {
        Connection conn = null;
        try {
            conn = DatabaseConnection.getConnection();

            // Check if Users table exists and count users
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM Users")) {
                if (rs.next()) {
                    return true;
                }
            } catch (SQLException e) {
                // Try with APP schema
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM APP.USERS")) {
                    if (rs.next()) {
                        return true;
                    }
                } catch (SQLException ex) {
                    // Ignore
                }
            }
            return true;
        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage() +
                              " (SQLState: " + e.getSQLState() + ")");
            if (e.getSQLState() != null && e.getSQLState().startsWith("XJ040")) {
                offerDatabaseRepair();
            }
            return false;
        } catch (ClassNotFoundException e) {
            System.err.println("Database driver not found: " + e.getMessage());
            return false;
        } catch (Exception e) {
            System.err.println("An error occurred: " + e.getMessage());
            return false;
        } finally {
            DatabaseConnection.closeConnection(conn);
        }
    }

    private static void offerDatabaseRepair() {
        int response = JOptionPane.showConfirmDialog(null,
            "The database appears to be corrupted. Would you like to repair it?",
            "Database Error", JOptionPane.YES_NO_OPTION);

        if (response == JOptionPane.YES_OPTION) {
            boolean repaired = DatabaseConnection.repairDatabase();
            if (repaired) {
                JOptionPane.showMessageDialog(null,
                    "Database has been repaired. Please restart the application.",
                    "Database Repaired", JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(null,
                    "Failed to repair database. Try manually deleting the 'lms' folder.",
                    "Repair Failed", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    public static void main(String[] args) {
        boolean success = testConnection();
        if (success) {
            JOptionPane.showMessageDialog(null,
                "Database connection test successful!",
                "Success", JOptionPane.INFORMATION_MESSAGE);
        } else {
            JOptionPane.showMessageDialog(null,
                "Database connection test failed.",
                "Error", JOptionPane.ERROR_MESSAGE);
        }
        System.exit(0);
    }
}
