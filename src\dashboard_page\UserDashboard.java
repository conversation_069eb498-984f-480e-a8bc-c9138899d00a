package dashboard_page;

import dashboard_page.panels.*;
import javax.swing.*;
import java.awt.*;
import library.management.system.login_register.Login;

public class UserDashboard extends javax.swing.JFrame {

    // Content panels
    private JPanel myLoansPanel;
    private JPanel myFinesPanel;

    /**
     * Creates new form UserDashboard
     */
    public UserDashboard() {
        // Remove window decoration (title bar, minimize, maximize, close buttons)
        // Must be called before initComponents() makes the frame displayable
        setUndecorated(true);

        initComponents();

        // Initialize content panels
        initializePanels();

        // Set up action listeners for navigation buttons
        setupActionListeners();

        // Show dashboard panel by default
        showPanel(dashboardPanel);

        // Set the frame to be maximized
        setExtendedState(JFrame.MAXIMIZED_BOTH);
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        dashboardBTN = new javax.swing.JPanel();
        userDashboardBTN = new javax.swing.JButton();
        myLoansBTN = new javax.swing.JButton();
        myFineBTN = new javax.swing.JButton();
        logoutBTN = new javax.swing.JButton();
        main_panel = new javax.swing.JPanel();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);
        setUndecorated(true);

        dashboardBTN.setBackground(new java.awt.Color(51, 102, 255));
        dashboardBTN.setPreferredSize(new java.awt.Dimension(300, 0));

        userDashboardBTN.setText("Dashboard");

        myLoansBTN.setText("My Borrow");

        myFineBTN.setText("My Fines");

        logoutBTN.setText("Logout");

        javax.swing.GroupLayout dashboardBTNLayout = new javax.swing.GroupLayout(dashboardBTN);
        dashboardBTN.setLayout(dashboardBTNLayout);
        dashboardBTNLayout.setHorizontalGroup(
            dashboardBTNLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(dashboardBTNLayout.createSequentialGroup()
                .addGap(18, 18, 18)
                .addGroup(dashboardBTNLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(userDashboardBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(myLoansBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(myFineBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(logoutBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 264, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addContainerGap(18, Short.MAX_VALUE))
        );
        dashboardBTNLayout.setVerticalGroup(
            dashboardBTNLayout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(dashboardBTNLayout.createSequentialGroup()
                .addGap(240, 240, 240)
                .addComponent(userDashboardBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(20, 20, 20)
                .addComponent(myLoansBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(20, 20, 20)
                .addComponent(myFineBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, 560, Short.MAX_VALUE)
                .addComponent(logoutBTN, javax.swing.GroupLayout.PREFERRED_SIZE, 50, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(40, 40, 40))
        );

        main_panel.setPreferredSize(new java.awt.Dimension(1620, 1080));
        main_panel.setLayout(new BorderLayout());

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(dashboardBTN, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(main_panel, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addComponent(dashboardBTN, javax.swing.GroupLayout.DEFAULT_SIZE, 1080, Short.MAX_VALUE)
            .addComponent(main_panel, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    /**
     * Initialize all content panels
     */
    private void initializePanels() {
        try {
            dashboardPanel = new dashboard_page.panels.UserDashboardContentPanel();
            myLoansPanel = new dashboard_page.panels.MyLoansContentPanel();
            myFinesPanel = new dashboard_page.panels.MyFinesContentPanel();
        } catch (Exception e) {
            System.err.println("Error initializing panels: " + e.getMessage());
            e.printStackTrace();

            // Create placeholder panels if there's an error
            dashboardPanel = createPlaceholderPanel("Dashboard");
            myLoansPanel = createPlaceholderPanel("My Loans");
            myFinesPanel = createPlaceholderPanel("My Fines");
        }
    }

    /**
     * Create a placeholder panel for features that encounter errors
     * @param title The title to display in the panel
     * @return A simple JPanel with a message
     */
    private JPanel createPlaceholderPanel(String title) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(Color.WHITE);

        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setHorizontalAlignment(JLabel.CENTER);

        JLabel messageLabel = new JLabel("This panel could not be loaded");
        messageLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        messageLabel.setHorizontalAlignment(JLabel.CENTER);

        JPanel centerPanel = new JPanel(new GridLayout(2, 1));
        centerPanel.setBackground(Color.WHITE);
        centerPanel.add(titleLabel);
        centerPanel.add(messageLabel);

        panel.add(centerPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * Set up action listeners for all navigation buttons
     */
    private void setupActionListeners() {
        // Dashboard button
        userDashboardBTN.addActionListener(e -> showPanel(dashboardPanel));

        // My Loans button
        myLoansBTN.addActionListener(e -> showPanel(myLoansPanel));

        // My Fines button
        myFineBTN.addActionListener(e -> showPanel(myFinesPanel));

        // Logout button
        logoutBTN.addActionListener(e -> logout());
    }

    /**
     * Show the specified panel in the main content area
     * @param panel The panel to display
     */
    private void showPanel(JPanel panel) {
        // Clear the main panel
        main_panel.removeAll();

        // Create a refresh button
        JButton refreshButton = new JButton("Refresh Data");
        refreshButton.addActionListener(e -> refreshCurrentPanel());

        // Create a top panel for the refresh button
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        topPanel.add(refreshButton);

        // Create a wrapper panel to hold both the content and the refresh button
        JPanel wrapperPanel = new JPanel(new BorderLayout());
        wrapperPanel.add(topPanel, BorderLayout.NORTH);
        wrapperPanel.add(panel, BorderLayout.CENTER);

        // Add the wrapper panel to the main panel
        main_panel.add(wrapperPanel, BorderLayout.CENTER);

        // Refresh the panel
        main_panel.revalidate();
        main_panel.repaint();

        // Debug message to confirm panel switch
        System.out.println("Switched to panel: " + panel.getClass().getSimpleName());

        // Refresh data if it's a dashboard panel
        if (panel instanceof UserDashboardContentPanel) {
            ((UserDashboardContentPanel) panel).loadDashboardData();
        }
    }

    /**
     * Refresh the currently displayed panel
     */
    public void refreshCurrentPanel() {
        Component[] components = main_panel.getComponents();
        if (components.length > 0 && components[0] instanceof JPanel) {
            JPanel wrapperPanel = (JPanel) components[0];

            // Get the content panel from the wrapper (it's in the CENTER position)
            Component[] wrapperComponents = wrapperPanel.getComponents();
            for (Component comp : wrapperComponents) {
                if (comp instanceof JPanel && wrapperPanel.getLayout() instanceof BorderLayout) {
                    Object constraints = ((BorderLayout)wrapperPanel.getLayout()).getConstraints(comp);
                    if (constraints == BorderLayout.CENTER && comp instanceof JPanel) {
                        JPanel contentPanel = (JPanel) comp;

                        if (contentPanel instanceof UserDashboardContentPanel) {
                            ((UserDashboardContentPanel) contentPanel).loadDashboardData();
                            System.out.println("Refreshed User Dashboard panel");
                        } else if (contentPanel instanceof MyLoansContentPanel) {
                            // Refresh my loans panel
                            ((MyLoansContentPanel) contentPanel).loadLoansData();
                            System.out.println("Refreshed My Loans panel");
                        }
                        break;
                    }
                }
            }
        }
    }

    /**
     * Handle logout action
     */
    private void logout() {
        int confirm = JOptionPane.showConfirmDialog(
            this,
            "Are you sure you want to logout?",
            "Confirm Logout",
            JOptionPane.YES_NO_OPTION
        );

        if (confirm == JOptionPane.YES_OPTION) {
            // Close the dashboard
            this.dispose();

            // Show the login form
            SwingUtilities.invokeLater(() -> {
                new Login().setVisible(true);
            });
        }
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(UserDashboard.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(UserDashboard.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(UserDashboard.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(UserDashboard.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new UserDashboard().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JPanel dashboardBTN;
    private javax.swing.JButton logoutBTN;
    private javax.swing.JPanel main_panel;
    private javax.swing.JButton myFineBTN;
    private javax.swing.JButton myLoansBTN;
    private javax.swing.JButton userDashboardBTN;
    // End of variables declaration//GEN-END:variables

    // Additional variables
    private JPanel dashboardPanel;
}
