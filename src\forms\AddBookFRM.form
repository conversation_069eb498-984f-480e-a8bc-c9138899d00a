<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
    <AuxValue name="designerSize" type="java.awt.Dimension" value="-84,-19,0,5,115,114,0,18,106,97,118,97,46,97,119,116,46,68,105,109,101,110,115,105,111,110,65,-114,-39,-41,-84,95,68,20,2,0,2,73,0,6,104,101,105,103,104,116,73,0,5,119,105,100,116,104,120,112,0,0,1,44,0,0,1,-112"/>
  </AuxValues>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="cc" green="cc" red="cc" type="rgb"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" max="-2" attributes="0"/>
                  <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="30" max="32767" attributes="0"/>
                  <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="jLabel1">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="24" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="ADD BOOK"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="jPanel2">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="cc" green="cc" red="cc" type="rgb"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="21" max="-2" attributes="0"/>
                  <Component id="closeBTN" min="-2" pref="221" max="-2" attributes="0"/>
                  <EmptySpace pref="56" max="32767" attributes="0"/>
                  <Component id="addBTN" min="-2" pref="197" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="21" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="30" max="32767" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="closeBTN" alignment="3" min="-2" pref="36" max="-2" attributes="0"/>
                      <Component id="addBTN" alignment="3" min="-2" pref="36" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JButton" name="addBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="ADD"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="addBTNActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="closeBTN">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="18" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="CLOSE"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="closeBTNActionPerformed"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="text" type="java.lang.String" value="ISBN"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="isbn">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="Title"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="title">
    </Component>
    <Component class="javax.swing.JTextField" name="publisher">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="text" type="java.lang.String" value="Publisher"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="text" type="java.lang.String" value="Publication Year"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="publication_year">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel6">
      <Properties>
        <Property name="text" type="java.lang.String" value="Genre"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="genre">
    </Component>
    <Component class="javax.swing.JTextField" name="edition">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel7">
      <Properties>
        <Property name="text" type="java.lang.String" value="Edition"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel8">
      <Properties>
        <Property name="text" type="java.lang.String" value="Copyright"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="copyright">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel10">
      <Properties>
        <Property name="text" type="java.lang.String" value="Copies"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="copies">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel11">
      <Properties>
        <Property name="text" type="java.lang.String" value="Status"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JComboBox" name="statusComboBox">
      <Properties>
        <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
          <StringArray count="5">
            <StringItem index="0" value="Available"/>
            <StringItem index="1" value="Loaned"/>
            <StringItem index="2" value="Reserved"/>
            <StringItem index="3" value="Under Repair"/>
            <StringItem index="4" value="Lost"/>
          </StringArray>
        </Property>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel12">
      <Properties>
        <Property name="text" type="java.lang.String" value="Acquisition Date"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel13">
      <Properties>
        <Property name="text" type="java.lang.String" value="Condition"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="condition">
    </Component>
  </SubComponents>
</Form>
