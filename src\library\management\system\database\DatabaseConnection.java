package library.management.system.database;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.io.File;
import java.io.FilenameFilter;

public class DatabaseConnection {
    private static final String DRIVER = "org.apache.derby.jdbc.EmbeddedDriver";
    private static boolean databaseShutdownRegistered = false;

    // Use a method to get the URL to ensure it's always using a relative path
    private static String getDatabaseURL() {
        // Get the current working directory
        String userDir = System.getProperty("user.dir");
        // Create the database directory if it doesn't exist
        File dbDir = new File(userDir, "lms");
        if (!dbDir.exists()) {
            dbDir.mkdirs();
        }

        // Return the URL with the relative path
        return "**************************";
    }

    public static Connection getConnection() throws SQLException, ClassNotFoundException {
        try {
            // Register shutdown hook if not already registered
            if (!databaseShutdownRegistered) {
                Runtime.getRuntime().addShutdownHook(new Thread(() -> shutdownDatabase()));
                databaseShutdownRegistered = true;
            }

            // Check if the database is already in use
            if (isDatabaseInUse()) {
                System.out.println("Database appears to be in use. Attempting to repair...");
                repairDatabase();
            }

            Class.forName(DRIVER);
            Connection conn = DriverManager.getConnection(getDatabaseURL());
            initializeDatabase(conn);
            return conn;
        } catch (ClassNotFoundException e) {
            System.err.println("Derby JDBC driver not found: " + e.getMessage());
            throw e;
        } catch (SQLException e) {
            System.err.println("Database connection error: " + e.getMessage() +
                " (SQLState: " + e.getSQLState() + ", Error Code: " + e.getErrorCode() + ")");

            // Check for known database corruption or locking errors
            if (e.getSQLState() != null &&
                (e.getSQLState().startsWith("XJ040") ||
                 e.getSQLState().equals("XJ041") ||
                 e.getSQLState().equals("XBM0A") ||
                 e.getSQLState().equals("XSDB6"))) {

                System.err.println("Database may be locked or corrupted. Attempting automatic repair...");

                // Try to repair the database
                if (repairDatabase()) {
                    System.out.println("Database repair completed. Please restart the application.");

                    // Try to connect again after repair
                    try {
                        // Wait a moment before trying again
                        Thread.sleep(1000);
                        return getConnection();
                    } catch (Exception retryEx) {
                        System.err.println("Failed to reconnect after repair: " + retryEx.getMessage());
                    }
                }

                // Print the full stack trace for debugging
                e.printStackTrace();

                // Check if there's a nested exception with more details
                if (e.getCause() != null) {
                    System.err.println("Caused by: " + e.getCause().getMessage());
                }
            }
            throw e;
        }
    }

    private static void initializeDatabase(Connection conn) {
        try {
            try {
                createUsersTable(conn);
            } catch (SQLException e) {
                if (e.getSQLState() == null || !e.getSQLState().equals("X0Y32")) {
                    System.err.println("Error creating Users table: " + e.getMessage());
                }
            }

            try {
                createBooksTable(conn);
            } catch (SQLException e) {
                if (e.getSQLState() == null || !e.getSQLState().equals("X0Y32")) {
                    System.err.println("Error creating Books table: " + e.getMessage());
                }
            }

            try {
                createBookCopiesTable(conn);
            } catch (SQLException e) {
                if (e.getSQLState() == null || !e.getSQLState().equals("X0Y32")) {
                    System.err.println("Error creating BookCopies table: " + e.getMessage());
                }
            }

            try {
                createLoansTable(conn);
            } catch (SQLException e) {
                if (e.getSQLState() == null || !e.getSQLState().equals("X0Y32")) {
                    System.err.println("Error creating Loans table: " + e.getMessage());
                }
            }

            try {
                createFinesTable(conn);
            } catch (SQLException e) {
                if (e.getSQLState() == null || !e.getSQLState().equals("X0Y32")) {
                    System.err.println("Error creating Fines table: " + e.getMessage());
                }
            }

            // Try to add the COPIES column to the BookCopies table if it doesn't exist
            try {
                addCopiesColumnIfNotExists(conn);
            } catch (SQLException e) {
                System.err.println("Error adding COPIES column: " + e.getMessage());
            }

            // Try to add the Status column to the Loans table if it doesn't exist
            try {
                addStatusColumnToLoansIfNotExists(conn);
            } catch (SQLException e) {
                System.err.println("Error adding Status column to Loans table: " + e.getMessage());
            }

            // Try to add the PaymentDate column to the Fines table if it doesn't exist
            try {
                addPaymentDateColumnToFinesIfNotExists(conn);
            } catch (SQLException e) {
                System.err.println("Error adding PaymentDate column to Fines table: " + e.getMessage());
            }

            // Add Copyright column if it doesn't exist
            try {
                addCopyrightColumnIfNotExists(conn);
            } catch (SQLException e) {
                System.err.println("Error adding Copyright column: " + e.getMessage());
            }

            // Add Copies column to Books table if it doesn't exist
            try {
                addCopiesColumnToBooksIfNotExists(conn);
            } catch (SQLException e) {
                System.err.println("Error adding Copies column to Books table: " + e.getMessage());
            }

            createSeedLibrarian(conn);
        } catch (Exception e) {
            System.err.println("Error initializing database: " + e.getMessage());
        }
    }

    private static void createUsersTable(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            stmt.execute("CREATE TABLE Users (" +
                "UserID INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY, " +
                "Username VARCHAR(50) NOT NULL UNIQUE, " +
                "PasswordHash VARCHAR(255) NOT NULL, " +
                "PhoneNumber VARCHAR(15), " +
                "Address VARCHAR(255), " +
                "Email VARCHAR(100) NOT NULL UNIQUE, " +
                "FullName VARCHAR(100) NOT NULL, " +
                "Role VARCHAR(20) NOT NULL, " +
                "RegistrationDate TIMESTAMP NOT NULL, " +
                "LastLoginDate TIMESTAMP" +
                ")");
        }
    }

    private static void createBooksTable(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            stmt.execute("CREATE TABLE Books (" +
                "BookID INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY, " +
                "ISBN VARCHAR(255) NOT NULL UNIQUE, " +
                "Title VARCHAR(255) NOT NULL, " +
                "Author VARCHAR(255), " +
                "Publisher VARCHAR(255), " +
                "PublicationYear INTEGER, " +
                "Copyright VARCHAR(50), " +  // Add Copyright field
                "Genre VARCHAR(255), " +
                "Edition VARCHAR(255), " +
                "Copies INTEGER DEFAULT 0, " +
                "Status VARCHAR(50) DEFAULT 'Available', " +
                "AcquisitionDate DATE, " +
                "Condition VARCHAR(255), " +
                "DateAddedToCatalog TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")");
        }
    }

    private static void createBookCopiesTable(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            stmt.execute("CREATE TABLE BookCopies (" +
                "CopyID INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY, " +
                "BookID INTEGER, " +
                "Copies INTEGER DEFAULT 0, " +
                "Status VARCHAR(50) DEFAULT 'Available', " +
                "AcquisitionDate DATE, " +
                "Condition VARCHAR(255), " +
                "FOREIGN KEY (BookID) REFERENCES Books(BookID)" +
                ")");
        }
    }

    private static void createLoansTable(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            stmt.execute("CREATE TABLE Loans (" +
                "LoanID INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY, " +
                "CopyID INTEGER NOT NULL, " +
                "UserID INTEGER NOT NULL, " +
                "IssuedByUserID INTEGER NOT NULL, " +
                "LoanDate DATE DEFAULT CURRENT_DATE NOT NULL, " +
                "DueDate DATE NOT NULL, " +
                "ReturnDate DATE, " +
                "Renewals INTEGER DEFAULT 0 NOT NULL, " +
                "Status VARCHAR(20) DEFAULT 'Pending', " +  // Added Status column with default 'Pending'
                "FOREIGN KEY (CopyID) REFERENCES BookCopies(CopyID), " +
                "FOREIGN KEY (UserID) REFERENCES Users(UserID), " +
                "FOREIGN KEY (IssuedByUserID) REFERENCES Users(UserID)" +
                ")");
        }
    }

    private static void createFinesTable(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement()) {
            stmt.execute("CREATE TABLE Fines (" +
                "FineID INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY, " +
                "LoanID INTEGER NOT NULL, " +
                "UserID INTEGER NOT NULL, " +
                "Amount DECIMAL(10,2) NOT NULL, " +
                "FineDate DATE DEFAULT CURRENT_DATE NOT NULL, " +
                "Reason VARCHAR(255), " +
                "Status VARCHAR(50) DEFAULT 'Unpaid' NOT NULL, " +
                "PaymentDate DATE, " +
                "FOREIGN KEY (LoanID) REFERENCES Loans(LoanID) ON DELETE RESTRICT, " +
                "FOREIGN KEY (UserID) REFERENCES Users(UserID) ON DELETE RESTRICT" +
                ")");
            System.out.println("Created Fines table");
        }
    }

    /**
     * Add Status column to Loans table if it doesn't exist
     */
    private static void addStatusColumnToLoansIfNotExists(Connection conn) throws SQLException {
        // Check if the Status column exists
        boolean columnExists = false;
        try {
            // Try to query the Status column to see if it exists
            try (Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SELECT Status FROM Loans WHERE 1=0");
                columnExists = true;
            }
        } catch (SQLException e) {
            // Column doesn't exist, continue with adding it
            columnExists = false;
        }

        // If the column doesn't exist, add it
        if (!columnExists) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ALTER TABLE Loans ADD COLUMN Status VARCHAR(20) DEFAULT 'Pending'");
                System.out.println("Added Status column to Loans table");
            } catch (SQLException e) {
                // Try with APP schema if the default schema fails
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute("ALTER TABLE APP.Loans ADD COLUMN Status VARCHAR(20) DEFAULT 'Pending'");
                    System.out.println("Added Status column to APP.Loans table");
                } catch (SQLException ex) {
                    System.err.println("Failed to add Status column to Loans table: " + ex.getMessage());
                    throw ex;
                }
            }
        }
    }

    private static void addCopiesColumnIfNotExists(Connection conn) throws SQLException {
        // Check if the COPIES column exists
        boolean columnExists = false;
        try {
            // Try to query the COPIES column to see if it exists
            try (Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SELECT COPIES FROM BookCopies WHERE 1=0");
                columnExists = true;
            }
        } catch (SQLException e) {
            // Column doesn't exist, continue with adding it
            columnExists = false;
        }

        // If the column doesn't exist, add it
        if (!columnExists) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ALTER TABLE BookCopies ADD COLUMN Copies INTEGER DEFAULT 0");
                System.out.println("Added COPIES column to BookCopies table");
            } catch (SQLException e) {
                // Try with APP schema if the default schema fails
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute("ALTER TABLE APP.BookCopies ADD COLUMN Copies INTEGER DEFAULT 0");
                    System.out.println("Added COPIES column to APP.BookCopies table");
                } catch (SQLException ex) {
                    System.err.println("Failed to add COPIES column to BookCopies table: " + ex.getMessage());
                    throw ex;
                }
            }
        }
    }

    /**
     * Add PaymentDate column to Fines table if it doesn't exist
     */
    private static void addPaymentDateColumnToFinesIfNotExists(Connection conn) throws SQLException {
        // Check if the PaymentDate column exists
        boolean columnExists = false;
        try {
            // Try to query the PaymentDate column to see if it exists
            try (Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SELECT PaymentDate FROM Fines WHERE 1=0");
                columnExists = true;
            }
        } catch (SQLException e) {
            // Column doesn't exist, continue with adding it
            columnExists = false;
        }

        // If the column doesn't exist, add it
        if (!columnExists) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ALTER TABLE Fines ADD COLUMN PaymentDate DATE");
                System.out.println("Added PaymentDate column to Fines table");
            } catch (SQLException e) {
                // Try with APP schema if the default schema fails
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute("ALTER TABLE APP.Fines ADD COLUMN PaymentDate DATE");
                    System.out.println("Added PaymentDate column to APP.Fines table");
                } catch (SQLException ex) {
                    System.err.println("Failed to add PaymentDate column to Fines table: " + ex.getMessage());
                    throw ex;
                }
            }
        }
    }

    /**
     * Add Copyright column to Books table if it doesn't exist
     */
    private static void addCopyrightColumnIfNotExists(Connection conn) throws SQLException {
        // Check if the Copyright column exists
        boolean columnExists = false;
        try {
            // Try to query the Copyright column to see if it exists
            try (Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SELECT Copyright FROM Books WHERE 1=0");
                columnExists = true;
            }
        } catch (SQLException e) {
            // Column doesn't exist, continue with adding it
            columnExists = false;
        }

        // If the column doesn't exist, add it
        if (!columnExists) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ALTER TABLE Books ADD COLUMN Copyright VARCHAR(50)");
                System.out.println("Added Copyright column to Books table");
            } catch (SQLException e) {
                // Try with APP schema if the default schema fails
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute("ALTER TABLE APP.Books ADD COLUMN Copyright VARCHAR(50)");
                    System.out.println("Added Copyright column to APP.Books table");
                } catch (SQLException ex) {
                    System.err.println("Failed to add Copyright column to Books table: " + ex.getMessage());
                    throw ex;
                }
            }
        }
    }

    /**
     * Add Copies column to Books table if it doesn't exist
     */
    private static void addCopiesColumnToBooksIfNotExists(Connection conn) throws SQLException {
        // Check if the Copies column exists in Books table
        boolean columnExists = false;
        try {
            // Try to query the Copies column to see if it exists
            try (Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SELECT Copies FROM Books WHERE 1=0");
                columnExists = true;
            }
        } catch (SQLException e) {
            // Column doesn't exist, continue with adding it
            columnExists = false;
        }

        // If the column doesn't exist, add it
        if (!columnExists) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("ALTER TABLE Books ADD COLUMN Copies INTEGER DEFAULT 0");
                System.out.println("Added Copies column to Books table");
            } catch (SQLException e) {
                // Try with APP schema if the default schema fails
                try (Statement stmt = conn.createStatement()) {
                    stmt.execute("ALTER TABLE APP.Books ADD COLUMN Copies INTEGER DEFAULT 0");
                    System.out.println("Added Copies column to APP.Books table");
                } catch (SQLException ex) {
                    System.err.println("Failed to add Copies column to Books table: " + ex.getMessage());
                    throw ex;
                }
            }
        }
    }

    private static void createSeedLibrarian(Connection conn) {
        try {
            int userCount = 0;
            try {
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM Users")) {
                    if (rs.next()) {
                        userCount = rs.getInt(1);
                    }
                } catch (SQLException e) {
                    try (Statement stmt = conn.createStatement();
                         ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM APP.Users")) {
                        if (rs.next()) {
                            userCount = rs.getInt(1);
                        }
                    } catch (SQLException ex) {
                        System.err.println("Could not query Users table in APP schema: " + ex.getMessage());
                    }
                }
            } catch (Exception e) {
                System.err.println("Error checking user count: " + e.getMessage());
            }

            if (userCount == 0) {
                String hashedPassword = hashPassword("admin123");
                String sql = "INSERT INTO Users (Username, PasswordHash, PhoneNumber, Address, Email, FullName, Role, RegistrationDate) " +
                            "VALUES ('admin', ?, '************', 'Library Main Office', '<EMAIL>', 'Admin', 'Librarian', CURRENT_TIMESTAMP)";

                try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                    pstmt.setString(1, hashedPassword);
                    pstmt.executeUpdate();
                } catch (SQLException e) {
                    System.err.println("Failed to insert seed librarian in default schema: " + e.getMessage());
                    try {
                        sql = "INSERT INTO APP.Users (Username, PasswordHash, PhoneNumber, Address, Email, FullName, Role, RegistrationDate) " +
                              "VALUES ('admin', ?, '************', 'Library Main Office', '<EMAIL>', 'System Administrator', 'Librarian', CURRENT_TIMESTAMP)";

                        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                            pstmt.setString(1, hashedPassword);
                            pstmt.executeUpdate();
                            System.out.println("Seed librarian account created in APP schema");
                        }
                    } catch (SQLException ex) {
                        System.err.println("Failed to insert seed librarian in APP schema: " + ex.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error creating seed librarian: " + e.getMessage());
        }
    }

    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                // Ignore
            }
        }
    }

    public static void shutdownDatabase() {
        try {
            // First try to shut down the specific database
            try {
                DriverManager.getConnection("****************************");
            } catch (SQLException e) {
                // Ignore - this is expected as Derby throws an exception on successful shutdown
            }

            // Then shut down the entire Derby system
            try {
                DriverManager.getConnection("*************************");
            } catch (SQLException e) {
                // A successful shutdown always throws SQLException XJ015
                if (e.getSQLState() != null && e.getSQLState().equals("XJ015")) {
                    System.out.println("Derby database system shut down normally");
                } else {
                    // This is also normal for single database shutdown
                    System.out.println("Derby database shut down");
                }
            }
        } catch (Exception e) {
            System.err.println("Error during shutdown: " + e.getMessage());
        }
    }

    public static boolean repairDatabase() {
        try {
            System.out.println("Starting database connection reset process...");

            // First, try to shut down the database properly
            shutdownDatabase();

            // Wait a moment to ensure the database is fully shut down
            try {
                System.out.println("Waiting for database to shut down completely...");
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                // Ignore
            }

            // Check for derby.log file and delete it if it exists
            File derbyLog = new File("derby.log");
            if (derbyLog.exists()) {
                if (derbyLog.delete()) {
                    System.out.println("Deleted derby.log file");
                } else {
                    System.out.println("Could not delete derby.log file");
                }
            }

            // Check for lock files in the database directory
            File dbDir = new File("lms");
            if (dbDir.exists() && dbDir.isDirectory()) {
                File[] lockFiles = dbDir.listFiles(new FilenameFilter() {
                    @Override
                    public boolean accept(File dir, String name) {
                        return name.toLowerCase().contains("lock");
                    }
                });
                if (lockFiles != null) {
                    for (File lockFile : lockFiles) {
                        if (lockFile.delete()) {
                            System.out.println("Deleted lock file: " + lockFile.getName());
                        } else {
                            System.out.println("Could not delete lock file: " + lockFile.getName());
                        }
                    }
                }
            }

            return true;
        } catch (Exception e) {
            System.err.println("Error during database connection reset: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }



    /**
     * Checks if the database is already in use by another process
     * @return true if the database is in use, false otherwise
     */
    public static boolean isDatabaseInUse() {
        File dbDir = new File("lms");
        if (dbDir.exists() && dbDir.isDirectory()) {
            File[] lockFiles = dbDir.listFiles(new FilenameFilter() {
                @Override
                public boolean accept(File dir, String name) {
                    return name.toLowerCase().contains("lock");
                }
            });
            return lockFiles != null && lockFiles.length > 0;
        }
        return false;
    }

    private static String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            return password;
        }
    }
}
