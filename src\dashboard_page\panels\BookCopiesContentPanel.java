package dashboard_page.panels;

import forms.AddBookCopiesFRM;
import forms.EditBookCopiesFRM;
import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;

import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

import library.management.system.database.DatabaseConnection;

public class BookCopiesContentPanel extends JPanel {

    // Static instance for access from other panels
    private static BookCopiesContentPanel instance;

    private JTable bookCopiesTable;
    private JTextField searchField;
    private JButton searchButton;
    private JButton editButton;
    private JButton deleteButton;
    private JButton refreshButton;
    private DefaultTableModel tableModel;

    public BookCopiesContentPanel() {
        initComponents();
        loadBookCopiesData();
        instance = this;
    }

    /**
     * Static method to refresh the book copies data from other panels
     */
    public static void refreshData() {
        if (instance != null) {
            instance.loadBookCopiesData();
        }
    }

    public void loadBookCopiesData() {
        try {
            tableModel.setRowCount(0);

            // Disable buttons when refreshing the table
            editButton.setEnabled(false);
            deleteButton.setEnabled(false);

            Connection conn = DatabaseConnection.getConnection();

            // Get data from BookCopies table and join with Books to get the title
            // Show all book copies regardless of status
            String sql = "SELECT bc.CopyID, bc.BookID, b.Title, bc.Copies, bc.Status, bc.AcquisitionDate, bc.Condition " +
                         "FROM BookCopies bc " +
                         "JOIN Books b ON bc.BookID = b.BookID " +
                         "ORDER BY bc.CopyID";

            PreparedStatement pstmt = conn.prepareStatement(sql);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                // Get data from result set
                String copyId = rs.getObject("CopyID") != null ? rs.getString("CopyID") : "NULL";
                String bookId = rs.getString("BookID");
                String title = rs.getString("Title");

                // Get copies count from database
                String copies = "0"; // Default value
                if (rs.getObject("Copies") != null) {
                    copies = rs.getString("Copies");
                }

                // Handle null values for book copy fields
                String status = rs.getObject("Status") != null ? rs.getString("Status") : "NULL";

                // Override status based on copies count only
                // If copies is 0, show as Unavailable
                // If copies > 0, always show as Available regardless of actual status
                try {
                    int copiesCount = Integer.parseInt(copies);
                    if (copiesCount <= 0) {
                        status = "Unavailable";
                    } else {
                        // Always show as Available if there are copies, even if some are loaned
                        status = "Available";
                    }
                } catch (NumberFormatException e) {
                    // If copies is not a valid number, keep the original status
                }

                // Format date if it exists
                String acquisitionDateStr = "NULL";
                if (rs.getObject("AcquisitionDate") != null) {
                    java.sql.Date acquisitionDate = rs.getDate("AcquisitionDate");
                    acquisitionDateStr = acquisitionDate.toString();
                }

                // Get condition as string (stored as VARCHAR)
                String conditionStr = "NULL";
                if (rs.getObject("Condition") != null) {
                    conditionStr = rs.getString("Condition");
                    // Handle case where the condition might be stored as "true" or "false" strings
                    if ("true".equalsIgnoreCase(conditionStr)) {
                        conditionStr = "Good";
                    } else if ("false".equalsIgnoreCase(conditionStr)) {
                        conditionStr = "Poor";
                    }
                }

                tableModel.addRow(new Object[]{
                    copyId,
                    title,
                    copies,
                    status,
                    acquisitionDateStr,
                    conditionStr
                });
            }

            rs.close();
            pstmt.close();

            // If no data found
            if (tableModel.getRowCount() == 0) {
                System.out.println("No book copies found in the database.");
                // No need to show a message dialog here
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            System.err.println("Error loading book copies: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error loading book copies: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));
        setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));

        searchField = new JTextField(20);

        searchField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyPressed(java.awt.event.KeyEvent e) {
                if (e.getKeyCode() == java.awt.event.KeyEvent.VK_ENTER) {
                    searchBookCopies();
                }
            }
        });

        searchButton = new JButton("Search");
        searchButton.setFont(new Font("Arial", Font.BOLD, 12));
        searchButton.setBackground(new Color(70, 130, 180));
        searchButton.setForeground(Color.WHITE);
        searchButton.addActionListener(e -> searchBookCopies());

        JButton clearButton = new JButton("Clear");
        clearButton.setFont(new Font("Arial", Font.BOLD, 12));
        clearButton.setBackground(new Color(180, 180, 180));
        clearButton.addActionListener(e -> {
            searchField.setText("");
            loadBookCopiesData();
        });

        searchPanel.add(new JLabel("Search:"));
        searchPanel.add(searchField);
        searchPanel.add(searchButton);
        searchPanel.add(clearButton);

        add(searchPanel, BorderLayout.NORTH);

        String[] columns = {"Copy ID", "Book Title", "Copies", "Status", "Acquisition Date", "Condition"};
        tableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        bookCopiesTable = new JTable(tableModel);
        bookCopiesTable.setFillsViewportHeight(true);
        bookCopiesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        bookCopiesTable.setRowHeight(35);

        bookCopiesTable.setShowGrid(true);
        bookCopiesTable.setGridColor(new Color(200, 200, 200));
        bookCopiesTable.setFont(new Font("Arial", Font.PLAIN, 12));

        JTableHeader header = bookCopiesTable.getTableHeader();
        header.setFont(new Font("Arial", Font.BOLD, 14));
        header.setBackground(new Color(220, 220, 220));
        header.setForeground(new Color(50, 50, 50));
        header.setPreferredSize(new Dimension(header.getWidth(), 40));

        ((DefaultTableCellRenderer)header.getDefaultRenderer()).setHorizontalAlignment(JLabel.CENTER);

        bookCopiesTable.getColumnModel().getColumn(0).setPreferredWidth(80);  // Copy ID
        bookCopiesTable.getColumnModel().getColumn(1).setPreferredWidth(300); // Book Title
        bookCopiesTable.getColumnModel().getColumn(2).setPreferredWidth(80);  // Copies
        bookCopiesTable.getColumnModel().getColumn(3).setPreferredWidth(100); // Status
        bookCopiesTable.getColumnModel().getColumn(4).setPreferredWidth(120); // Acquisition Date
        bookCopiesTable.getColumnModel().getColumn(5).setPreferredWidth(100); // Condition

        bookCopiesTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (!isSelected) {
                    c.setBackground(row % 2 == 0 ? new Color(245, 245, 245) : Color.WHITE);
                    c.setForeground(new Color(50, 50, 50));
                } else {
                    c.setBackground(new Color(230, 230, 250));
                    c.setForeground(new Color(50, 50, 50));
                }

                if (c instanceof JLabel) {
                    JLabel label = (JLabel) c;
                    label.setBorder(BorderFactory.createEmptyBorder(0, 8, 0, 8));

                    // Center align Copy ID, Copies, Status, Acquisition Date, and Condition columns
                    if (column == 0 || column == 2 || column == 3 || column == 4 || column == 5) {
                        label.setHorizontalAlignment(JLabel.CENTER);

                        // Color status column
                        if (column == 3 && value != null) {
                            String status = value.toString();
                            if ("Available".equals(status)) {
                                label.setForeground(new Color(0, 128, 0)); // Green
                            } else if ("Loaned".equals(status)) {
                                label.setForeground(new Color(220, 0, 0)); // Red
                            } else if ("Reserved".equals(status)) {
                                label.setForeground(new Color(0, 0, 220)); // Blue
                            } else if ("Damaged".equals(status)) {
                                label.setForeground(new Color(255, 140, 0)); // Orange
                            } else if ("Lost".equals(status)) {
                                label.setForeground(new Color(128, 0, 128)); // Purple
                            } else if ("Unavailable".equals(status)) {
                                label.setForeground(new Color(169, 169, 169)); // Dark Gray
                            }
                        }

                        // Color condition column
                        if (column == 5 && value != null) {
                            String condition = value.toString();
                            if ("Good".equals(condition)) {
                                label.setForeground(new Color(0, 128, 0)); // Green
                            } else if ("Poor".equals(condition) || "Damaged".equals(condition)) {
                                label.setForeground(new Color(255, 140, 0)); // Orange
                            } else if ("Very Poor".equals(condition)) {
                                label.setForeground(new Color(220, 0, 0)); // Red
                            }
                        }
                    } else {
                        // Left align Book Title column
                        label.setHorizontalAlignment(JLabel.LEFT);
                    }
                }

                return c;
            }
        });

        bookCopiesTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int selectedRow = bookCopiesTable.getSelectedRow();
                if (selectedRow >= 0) {
                    editButton.setEnabled(true);
                    deleteButton.setEnabled(true);
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(bookCopiesTable);
        add(scrollPane, BorderLayout.CENTER);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        buttonPanel.setBackground(new Color(240, 240, 240));

        JButton addButton = new JButton("Add Book Copy");
        addButton.setFont(new Font("Arial", Font.BOLD, 14));
        addButton.setBackground(new Color(46, 125, 50));
        addButton.setForeground(Color.WHITE);
        addButton.setFocusPainted(false);
        addButton.setPreferredSize(new Dimension(150, 40));
        addButton.addActionListener(e -> openAddBookCopyForm());
        addButton.setToolTipText("Add a new book copy");

        editButton = new JButton("Edit Book Copy");
        editButton.setFont(new Font("Arial", Font.BOLD, 14));
        editButton.setBackground(new Color(0, 102, 204));
        editButton.setForeground(Color.WHITE);
        editButton.setFocusPainted(false);
        editButton.setPreferredSize(new Dimension(150, 40));
        editButton.addActionListener(e -> openEditBookCopyForm());
        editButton.setEnabled(false);

        deleteButton = new JButton("Delete Book Copy");
        deleteButton.setFont(new Font("Arial", Font.BOLD, 14));
        deleteButton.setBackground(new Color(204, 0, 0));
        deleteButton.setForeground(Color.WHITE);
        deleteButton.setFocusPainted(false);
        deleteButton.setPreferredSize(new Dimension(150, 40));
        deleteButton.addActionListener(e -> deleteSelectedBookCopy());
        deleteButton.setEnabled(false);
        deleteButton.setToolTipText("Delete the selected book copy from the database");

        refreshButton = new JButton("Refresh");
        refreshButton.setFont(new Font("Arial", Font.BOLD, 14));
        refreshButton.setBackground(new Color(100, 181, 246));
        refreshButton.setForeground(Color.WHITE);
        refreshButton.setFocusPainted(false);
        refreshButton.setPreferredSize(new Dimension(120, 40));
        refreshButton.addActionListener(e -> loadBookCopiesData());

        buttonPanel.add(refreshButton);
        buttonPanel.add(addButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);

        add(buttonPanel, BorderLayout.SOUTH);
    }

    private void openAddBookCopyForm() {
        try {
            // Create and show the add form
            AddBookCopiesFRM addBookCopyForm = new AddBookCopiesFRM();
            addBookCopyForm.setVisible(true);
            addBookCopyForm.setLocationRelativeTo(null);

            // Refresh the table when the form is closed
            addBookCopyForm.addWindowListener(new java.awt.event.WindowAdapter() {
                @Override
                public void windowClosed(java.awt.event.WindowEvent e) {
                    loadBookCopiesData();
                }
            });
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "Error opening Add Book Copy form: " + e.getMessage(),
                "Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    private void openEditBookCopyForm() {
        int selectedRow = bookCopiesTable.getSelectedRow();
        if (selectedRow >= 0) {
            String copyId = bookCopiesTable.getValueAt(selectedRow, 0).toString();

            // We need to get the BookID from the database since it's not displayed in the table
            String title = bookCopiesTable.getValueAt(selectedRow, 1).toString();
            String copiesStr = bookCopiesTable.getValueAt(selectedRow, 2).toString();
            String status = bookCopiesTable.getValueAt(selectedRow, 3).toString();
            String acquisitionDateStr = bookCopiesTable.getValueAt(selectedRow, 4).toString();
            String conditionStr = bookCopiesTable.getValueAt(selectedRow, 5).toString();

            // Check if we're creating a new book copy (CopyID is NULL)
            boolean isNewCopy = "NULL".equals(copyId);

            // Get the BookID from the database using the CopyID
            String bookId = "";

            try {
                Connection conn = DatabaseConnection.getConnection();

                // First get the BookID and Author from the CopyID
                String author = "";

                if (!"NULL".equals(copyId)) {
                    String sql = "SELECT bc.BookID, b.Author FROM BookCopies bc JOIN Books b ON bc.BookID = b.BookID WHERE bc.CopyID = ?";
                    PreparedStatement pstmt = conn.prepareStatement(sql);
                    try {
                        pstmt.setInt(1, Integer.parseInt(copyId));
                        ResultSet rs = pstmt.executeQuery();

                        if (rs.next()) {
                            bookId = rs.getString("BookID");
                            author = rs.getString("Author");
                        } else {
                            JOptionPane.showMessageDialog(this,
                                "Could not find book copy with ID: " + copyId,
                                "Error",
                                JOptionPane.ERROR_MESSAGE);
                            rs.close();
                            pstmt.close();
                            DatabaseConnection.closeConnection(conn);
                            return;
                        }

                        rs.close();
                        pstmt.close();
                    } catch (NumberFormatException e) {
                        JOptionPane.showMessageDialog(this,
                            "Invalid Copy ID format: " + copyId,
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                        DatabaseConnection.closeConnection(conn);
                        return;
                    }
                } else {
                    // If it's a new copy, we need to get the BookID from the title
                    String sql = "SELECT BookID, Author FROM Books WHERE Title = ?";
                    PreparedStatement pstmt = conn.prepareStatement(sql);
                    pstmt.setString(1, title);
                    ResultSet rs = pstmt.executeQuery();

                    if (rs.next()) {
                        bookId = rs.getString("BookID");
                        author = rs.getString("Author");
                    } else {
                        JOptionPane.showMessageDialog(this,
                            "Could not find book with title: " + title,
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                        rs.close();
                        pstmt.close();
                        DatabaseConnection.closeConnection(conn);
                        return;
                    }

                    rs.close();
                    pstmt.close();
                }

                // Parse acquisition date
                java.sql.Date acquisitionDate = null;
                if (!"NULL".equals(acquisitionDateStr)) {
                    try {
                        java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                        java.util.Date parsedDate = dateFormat.parse(acquisitionDateStr);
                        acquisitionDate = new java.sql.Date(parsedDate.getTime());
                    } catch (Exception e) {
                        // If parsing fails, use today's date
                        acquisitionDate = new java.sql.Date(System.currentTimeMillis());
                    }
                } else {
                    // If NULL, use today's date
                    acquisitionDate = new java.sql.Date(System.currentTimeMillis());
                }

                // Parse copies
                int copies = 0;
                if (!"NULL".equals(copiesStr)) {
                    try {
                        copies = Integer.parseInt(copiesStr);
                    } catch (NumberFormatException e) {
                        // Ignore parsing errors, leave as 0
                    }
                }

                // If this is a new copy (CopyID is NULL), we need to INSERT instead of UPDATE
                if ("NULL".equals(copyId)) {
                    // Set default values for a new book copy
                    copyId = "0"; // Will be auto-generated by the database
                    status = "Available"; // Default status for new copies
                    conditionStr = "Good"; // Default condition for new copies

                    // Show a message to the user
                    JOptionPane.showMessageDialog(this,
                        "Creating a new book copy for: " + title,
                        "New Book Copy",
                        JOptionPane.INFORMATION_MESSAGE);
                }

                // Create and show the edit form with the data
                EditBookCopiesFRM editBookCopyForm = new EditBookCopiesFRM(
                    copyId, bookId, title, author, status,
                    acquisitionDate, conditionStr, copies
                );

                editBookCopyForm.setVisible(true);
                editBookCopyForm.setLocationRelativeTo(null);
                editBookCopyForm.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

                editBookCopyForm.addWindowListener(new java.awt.event.WindowAdapter() {
                    @Override
                    public void windowClosed(java.awt.event.WindowEvent e) {
                        loadBookCopiesData();
                    }
                });

                DatabaseConnection.closeConnection(conn);

            } catch (Exception e) {
                JOptionPane.showMessageDialog(this,
                    "Error opening Edit Book Copy form: " + e.getMessage(),
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    // Delete the selected book copy from the database
    private void deleteSelectedBookCopy() {
        int selectedRow = bookCopiesTable.getSelectedRow();
        if (selectedRow >= 0) {
            String copyId = bookCopiesTable.getValueAt(selectedRow, 0).toString();
            String bookTitle = bookCopiesTable.getValueAt(selectedRow, 1).toString();

            // Check if we're trying to delete a NULL copy (which doesn't exist in the database)
            if ("NULL".equals(copyId)) {
                JOptionPane.showMessageDialog(this,
                    "Cannot delete a book copy that hasn't been saved to the database.",
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
                return;
            }

            // Confirm deletion with the user
            int confirm = JOptionPane.showConfirmDialog(this,
                "Are you sure you want to delete this copy of \"" + bookTitle + "\"?",
                "Confirm Deletion",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

            if (confirm == JOptionPane.YES_OPTION) {
                try {
                    Connection conn = DatabaseConnection.getConnection();
                    int copyIdInt;

                    try {
                        copyIdInt = Integer.parseInt(copyId);
                    } catch (NumberFormatException e) {
                        JOptionPane.showMessageDialog(this,
                            "Invalid Copy ID format: " + copyId,
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                        DatabaseConnection.closeConnection(conn);
                        return;
                    }

                    // First check if there are any associated loan records
                    String checkLoansQuery = "SELECT COUNT(*) FROM Loans WHERE CopyID = ?";
                    PreparedStatement checkLoansStmt = conn.prepareStatement(checkLoansQuery);
                    checkLoansStmt.setInt(1, copyIdInt);
                    ResultSet loansRs = checkLoansStmt.executeQuery();

                    int loanCount = 0;
                    if (loansRs.next()) {
                        loanCount = loansRs.getInt(1);
                    }

                    loansRs.close();
                    checkLoansStmt.close();

                    if (loanCount > 0) {
                        // Ask user if they want to force delete by removing associated loans first
                        int forceDelete = JOptionPane.showConfirmDialog(this,
                            "This book copy has " + loanCount + " associated loan records.\n" +
                            "Deleting it will also delete all associated loan records.\n\n" +
                            "Do you want to force delete this book copy and its loan records?",
                            "Force Delete",
                            JOptionPane.YES_NO_OPTION,
                            JOptionPane.WARNING_MESSAGE);

                        if (forceDelete != JOptionPane.YES_OPTION) {
                            DatabaseConnection.closeConnection(conn);
                            return;
                        }

                        // Delete associated loan records first
                        String deleteLoansQuery = "DELETE FROM Loans WHERE CopyID = ?";
                        PreparedStatement deleteLoansStmt = conn.prepareStatement(deleteLoansQuery);
                        deleteLoansStmt.setInt(1, copyIdInt);
                        int loansDeleted = deleteLoansStmt.executeUpdate();
                        deleteLoansStmt.close();

                        System.out.println("Deleted " + loansDeleted + " loan records for copy ID " + copyIdInt);
                    }

                    // Now delete the book copy
                    String sql = "DELETE FROM BookCopies WHERE CopyID = ?";
                    PreparedStatement pstmt = conn.prepareStatement(sql);
                    pstmt.setInt(1, copyIdInt);

                    int rowsAffected = pstmt.executeUpdate();
                    pstmt.close();
                    DatabaseConnection.closeConnection(conn);

                    if (rowsAffected > 0) {
                        JOptionPane.showMessageDialog(this,
                            "Book copy deleted successfully!",
                            "Success",
                            JOptionPane.INFORMATION_MESSAGE);

                        // Refresh the table to show the updated data
                        loadBookCopiesData();

                        // Also refresh the loans panel if it exists
                        try {
                            dashboard_page.panels.LoansContentPanel.refreshData();
                        } catch (Exception e) {
                            // Ignore if the loans panel doesn't exist or can't be refreshed
                        }

                        try {
                            dashboard_page.panels.MyLoansContentPanel.refreshData();
                        } catch (Exception e) {
                            // Ignore if the my loans panel doesn't exist or can't be refreshed
                        }
                    } else {
                        JOptionPane.showMessageDialog(this,
                            "Failed to delete book copy. The copy may have already been deleted.",
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                    }

                } catch (Exception e) {
                    System.err.println("Error deleting book copy: " + e.getMessage());
                    e.printStackTrace();
                    JOptionPane.showMessageDialog(this,
                        "Error deleting book copy: " + e.getMessage(),
                        "Database Error",
                        JOptionPane.ERROR_MESSAGE);
                }
            }
        } else {
            JOptionPane.showMessageDialog(this,
                "Please select a book copy to delete.",
                "Information",
                JOptionPane.INFORMATION_MESSAGE);
        }
    }

    private void searchBookCopies() {
        String searchTerm = searchField.getText().trim();

        if (searchTerm.isEmpty()) {
            loadBookCopiesData();
            return;
        }

        try {
            tableModel.setRowCount(0);

            // Disable buttons when searching
            editButton.setEnabled(false);
            deleteButton.setEnabled(false);

            Connection conn = DatabaseConnection.getConnection();

            // Search in BookCopies table and join with Books to get the title
            String sql = "SELECT bc.CopyID, bc.BookID, b.Title, bc.Copies, bc.Status, bc.AcquisitionDate, bc.Condition " +
                         "FROM BookCopies bc " +
                         "JOIN Books b ON bc.BookID = b.BookID " +
                         "WHERE LOWER(b.Title) LIKE ? OR LOWER(b.Author) LIKE ? OR LOWER(b.ISBN) LIKE ? " +
                         "ORDER BY bc.CopyID";

            PreparedStatement pstmt = conn.prepareStatement(sql);

            String likeParam = "%" + searchTerm.toLowerCase() + "%";
            pstmt.setString(1, likeParam);
            pstmt.setString(2, likeParam);
            pstmt.setString(3, likeParam);

            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                // Get data from result set
                String copyId = rs.getObject("CopyID") != null ? rs.getString("CopyID") : "NULL";
                String bookId = rs.getString("BookID");
                String title = rs.getString("Title");

                // Get copies count from database
                String copies = "0"; // Default value
                if (rs.getObject("Copies") != null) {
                    copies = rs.getString("Copies");
                }

                // Handle null values for book copy fields
                String status = rs.getObject("Status") != null ? rs.getString("Status") : "NULL";

                // Override status based on copies count only
                // If copies is 0, show as Unavailable
                // If copies > 0, always show as Available regardless of actual status
                try {
                    int copiesCount = Integer.parseInt(copies);
                    if (copiesCount <= 0) {
                        status = "Unavailable";
                    } else {
                        // Always show as Available if there are copies, even if some are loaned
                        status = "Available";
                    }
                } catch (NumberFormatException e) {
                    // If copies is not a valid number, keep the original status
                }

                // Format date if it exists
                String acquisitionDateStr = "NULL";
                if (rs.getObject("AcquisitionDate") != null) {
                    java.sql.Date acquisitionDate = rs.getDate("AcquisitionDate");
                    acquisitionDateStr = acquisitionDate.toString();
                }

                // Get condition as string (stored as VARCHAR)
                String conditionStr = "NULL";
                if (rs.getObject("Condition") != null) {
                    conditionStr = rs.getString("Condition");
                    // Handle case where the condition might be stored as "true" or "false" strings
                    if ("true".equalsIgnoreCase(conditionStr)) {
                        conditionStr = "Good";
                    } else if ("false".equalsIgnoreCase(conditionStr)) {
                        conditionStr = "Poor";
                    }
                }

                tableModel.addRow(new Object[]{
                    copyId,
                    title,
                    copies,
                    status,
                    acquisitionDateStr,
                    conditionStr
                });
            }

            rs.close();
            pstmt.close();
            DatabaseConnection.closeConnection(conn);

            if (tableModel.getRowCount() == 0) {
                System.out.println("No books found matching your search criteria.");
                // No need to show a message dialog when no books are found
            }

        } catch (Exception e) {
            System.err.println("Error searching book copies: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error searching book copies: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }
}