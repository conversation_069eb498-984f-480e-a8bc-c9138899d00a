package forms;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import javax.swing.*;
import library.management.system.database.DatabaseConnection;

public class EditUserFRM extends javax.swing.JFrame {

    private int userId;
    private JTextField usernameField;
    private <PERSON>asswordField passwordField;
    private JPasswordField confirmPasswordField;
    private JTextField fullNameField;
    private JTextField emailField;
    private JTextField phoneField;
    private JTextArea addressArea;
    private JComboBox<String> roleComboBox;
    private JButton saveButton;
    private JButton cancelButton;
    private J<PERSON><PERSON>ckB<PERSON> changePasswordCheckbox;
    private JPanel passwordPanel;

    /**
     * Creates new form EditUserFRM
     * @param userId The ID of the user to edit
     */
    public EditUserFRM(int userId) {
        this.userId = userId;
        initComponents();
        loadUserData();
        setLocationRelativeTo(null);
        setTitle("Edit User");
    }

    private void initComponents() {
        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
        setResizable(false);

        // Create main panel with padding
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Create form panel
        JPanel formPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.insets = new Insets(5, 5, 5, 5);

        // Username
        gbc.gridx = 0;
        gbc.gridy = 0;
        formPanel.add(new JLabel("Username:"), gbc);

        gbc.gridx = 1;
        usernameField = new JTextField(20);
        formPanel.add(usernameField, gbc);

        // Change Password Checkbox
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 2;
        changePasswordCheckbox = new JCheckBox("Change Password");
        formPanel.add(changePasswordCheckbox, gbc);

        // Password Panel (initially invisible)
        passwordPanel = new JPanel(new GridBagLayout());
        passwordPanel.setVisible(false);

        GridBagConstraints pwdGbc = new GridBagConstraints();
        pwdGbc.fill = GridBagConstraints.HORIZONTAL;
        pwdGbc.insets = new Insets(5, 5, 5, 5);

        // Password
        pwdGbc.gridx = 0;
        pwdGbc.gridy = 0;
        passwordPanel.add(new JLabel("New Password:"), pwdGbc);

        pwdGbc.gridx = 1;
        passwordField = new JPasswordField(20);
        passwordPanel.add(passwordField, pwdGbc);

        // Confirm Password
        pwdGbc.gridx = 0;
        pwdGbc.gridy = 1;
        passwordPanel.add(new JLabel("Confirm Password:"), pwdGbc);

        pwdGbc.gridx = 1;
        confirmPasswordField = new JPasswordField(20);
        passwordPanel.add(confirmPasswordField, pwdGbc);

        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.gridwidth = 2;
        formPanel.add(passwordPanel, gbc);

        // Full Name
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.gridwidth = 1;
        formPanel.add(new JLabel("Full Name:"), gbc);

        gbc.gridx = 1;
        fullNameField = new JTextField(20);
        formPanel.add(fullNameField, gbc);

        // Email
        gbc.gridx = 0;
        gbc.gridy = 4;
        formPanel.add(new JLabel("Email:"), gbc);

        gbc.gridx = 1;
        emailField = new JTextField(20);
        formPanel.add(emailField, gbc);

        // Phone
        gbc.gridx = 0;
        gbc.gridy = 5;
        formPanel.add(new JLabel("Phone Number:"), gbc);

        gbc.gridx = 1;
        phoneField = new JTextField(20);
        formPanel.add(phoneField, gbc);

        // Address
        gbc.gridx = 0;
        gbc.gridy = 6;
        formPanel.add(new JLabel("Address:"), gbc);

        gbc.gridx = 1;
        addressArea = new JTextArea(3, 20);
        addressArea.setLineWrap(true);
        JScrollPane addressScrollPane = new JScrollPane(addressArea);
        formPanel.add(addressScrollPane, gbc);

        // Role
        gbc.gridx = 0;
        gbc.gridy = 7;
        formPanel.add(new JLabel("Role:"), gbc);

        gbc.gridx = 1;
        roleComboBox = new JComboBox<>(new String[]{"User", "Librarian"});
        formPanel.add(roleComboBox, gbc);

        // Button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        saveButton = new JButton("Save");
        cancelButton = new JButton("Cancel");

        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);

        // Add panels to main panel
        mainPanel.add(formPanel, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        // Add main panel to frame
        getContentPane().add(mainPanel);

        // Set up action listeners
        changePasswordCheckbox.addActionListener((ActionEvent e) -> {
            passwordPanel.setVisible(changePasswordCheckbox.isSelected());
            pack(); // Resize the frame to fit the new components
        });

        saveButton.addActionListener((ActionEvent e) -> {
            updateUser();
        });

        cancelButton.addActionListener((ActionEvent e) -> {
            dispose();
        });

        pack();
    }

    /**
     * Load user data from the database
     */
    private void loadUserData() {
        try {
            Connection conn = DatabaseConnection.getConnection();
            String query = "SELECT Username, FullName, Email, PhoneNumber, Address, Role FROM Users WHERE UserID = ?";

            try (PreparedStatement pstmt = conn.prepareStatement(query)) {
                pstmt.setInt(1, userId);

                try (ResultSet rs = pstmt.executeQuery()) {
                    if (rs.next()) {
                        // Populate form fields with user data
                        usernameField.setText(rs.getString("Username"));
                        fullNameField.setText(rs.getString("FullName"));
                        emailField.setText(rs.getString("Email"));
                        phoneField.setText(rs.getString("PhoneNumber"));
                        addressArea.setText(rs.getString("Address"));
                        roleComboBox.setSelectedItem(rs.getString("Role"));
                    } else {
                        JOptionPane.showMessageDialog(this,
                            "User not found",
                            "Error",
                            JOptionPane.ERROR_MESSAGE);
                        dispose(); // Close the form
                    }
                }
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            System.err.println("Error loading user data: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "Error loading user data: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
            dispose(); // Close the form
        }
    }

    /**
     * Update user in the database
     */
    private void updateUser() {
        // Validate input fields
        if (!validateInputs()) {
            return;
        }

        try {
            Connection conn = DatabaseConnection.getConnection();

            // Prepare SQL statement based on whether password is being changed
            String sql;
            if (changePasswordCheckbox.isSelected()) {
                sql = "UPDATE Users SET Username = ?, PasswordHash = ?, FullName = ?, Email = ?, PhoneNumber = ?, Address = ?, Role = ? WHERE UserID = ?";
            } else {
                sql = "UPDATE Users SET Username = ?, FullName = ?, Email = ?, PhoneNumber = ?, Address = ?, Role = ? WHERE UserID = ?";
            }

            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                int paramIndex = 1;

                // Set parameters
                pstmt.setString(paramIndex++, usernameField.getText().trim());

                // Add password hash if changing password
                if (changePasswordCheckbox.isSelected()) {
                    pstmt.setString(paramIndex++, hashPassword(new String(passwordField.getPassword())));
                }

                pstmt.setString(paramIndex++, fullNameField.getText().trim());
                pstmt.setString(paramIndex++, emailField.getText().trim());
                pstmt.setString(paramIndex++, phoneField.getText().trim());
                pstmt.setString(paramIndex++, addressArea.getText().trim());
                pstmt.setString(paramIndex++, roleComboBox.getSelectedItem().toString());
                pstmt.setInt(paramIndex, userId);

                // Execute the statement
                int rowsAffected = pstmt.executeUpdate();

                if (rowsAffected > 0) {
                    JOptionPane.showMessageDialog(this,
                        "User updated successfully",
                        "Success",
                        JOptionPane.INFORMATION_MESSAGE);
                    dispose(); // Close the form
                } else {
                    JOptionPane.showMessageDialog(this,
                        "Failed to update user",
                        "Error",
                        JOptionPane.ERROR_MESSAGE);
                }
            }

            DatabaseConnection.closeConnection(conn);

        } catch (Exception e) {
            System.err.println("Error updating user: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "Error updating user: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Validate user input fields
     * @return true if all inputs are valid, false otherwise
     */
    private boolean validateInputs() {
        // Check for empty fields
        if (usernameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "Username cannot be empty", "Validation Error", JOptionPane.ERROR_MESSAGE);
            usernameField.requestFocus();
            return false;
        }

        if (fullNameField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "Full Name cannot be empty", "Validation Error", JOptionPane.ERROR_MESSAGE);
            fullNameField.requestFocus();
            return false;
        }

        if (emailField.getText().trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "Email cannot be empty", "Validation Error", JOptionPane.ERROR_MESSAGE);
            emailField.requestFocus();
            return false;
        }

        // Check password fields if changing password
        if (changePasswordCheckbox.isSelected()) {
            if (passwordField.getPassword().length == 0) {
                JOptionPane.showMessageDialog(this, "New Password cannot be empty", "Validation Error", JOptionPane.ERROR_MESSAGE);
                passwordField.requestFocus();
                return false;
            }

            // Check if passwords match
            String password = new String(passwordField.getPassword());
            String confirmPassword = new String(confirmPasswordField.getPassword());

            if (!password.equals(confirmPassword)) {
                JOptionPane.showMessageDialog(this, "Passwords do not match", "Validation Error", JOptionPane.ERROR_MESSAGE);
                passwordField.requestFocus();
                return false;
            }
        }

        // Validate email format
        String email = emailField.getText().trim();
        if (!email.matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            JOptionPane.showMessageDialog(this, "Invalid email format", "Validation Error", JOptionPane.ERROR_MESSAGE);
            emailField.requestFocus();
            return false;
        }

        return true;
    }

    /**
     * Hash password using SHA-256
     * @param password The password to hash
     * @return The hashed password
     */
    private String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            return password; // Return unhashed password if hashing fails
        }
    }
}
