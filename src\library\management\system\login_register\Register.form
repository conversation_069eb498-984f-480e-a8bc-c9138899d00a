<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="jPanel3" max="32767" attributes="0"/>
          <Component id="jPanel2" alignment="0" max="32767" attributes="0"/>
          <Group type="102" alignment="1" attributes="0">
              <Group type="103" groupAlignment="1" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace max="32767" attributes="0"/>
                      <Component id="jCheckBox2" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" attributes="0">
                      <EmptySpace min="25" pref="25" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                              <EmptySpace type="separate" max="-2" attributes="0"/>
                              <Component id="full_name" max="32767" attributes="0"/>
                          </Group>
                          <Group type="102" attributes="0">
                              <Component id="jLabel3" min="-2" max="-2" attributes="0"/>
                              <EmptySpace type="separate" max="-2" attributes="0"/>
                              <Component id="username" pref="280" max="32767" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="jLabel7" alignment="0" min="-2" max="-2" attributes="0"/>
                                  <Component id="jLabel6" alignment="0" min="-2" max="-2" attributes="0"/>
                              </Group>
                              <EmptySpace min="-2" pref="23" max="-2" attributes="0"/>
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="email" max="32767" attributes="0"/>
                                  <Component id="password" max="32767" attributes="0"/>
                              </Group>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                                  <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                              </Group>
                              <EmptySpace type="separate" max="-2" attributes="0"/>
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="address" max="32767" attributes="0"/>
                                  <Component id="phone_no" max="32767" attributes="0"/>
                              </Group>
                          </Group>
                      </Group>
                  </Group>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jPanel2" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel1" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="full_name" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="username" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="phone_no" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="address" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel6" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="email" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel7" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="password" alignment="3" min="-2" pref="40" max="-2" attributes="0"/>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Component id="jCheckBox2" min="-2" max="-2" attributes="0"/>
              <EmptySpace pref="30" max="32767" attributes="0"/>
              <Component id="jPanel3" min="-2" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel3">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="99" green="99" red="99" type="rgb"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace min="-2" pref="18" max="-2" attributes="0"/>
                  <Component id="back" min="-2" pref="170" max="-2" attributes="0"/>
                  <EmptySpace max="32767" attributes="0"/>
                  <Component id="register" min="-2" pref="170" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="16" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="30" max="32767" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="register" alignment="3" min="-2" max="-2" attributes="0"/>
                      <Component id="back" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JButton" name="back">
          <Properties>
            <Property name="text" type="java.lang.String" value="BACK"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="backActionPerformed"/>
          </Events>
        </Component>
        <Component class="javax.swing.JButton" name="register">
          <Properties>
            <Property name="text" type="java.lang.String" value="Register"/>
          </Properties>
          <Events>
            <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="registerActionPerformed"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="jPanel2">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="99" green="99" red="99" type="rgb"/>
        </Property>
      </Properties>

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace max="32767" attributes="0"/>
                  <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="74" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="1" attributes="0">
                  <EmptySpace pref="30" max="32767" attributes="0"/>
                  <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="30" max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="jLabel2">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="24" style="1"/>
            </Property>
            <Property name="text" type="java.lang.String" value="REGISTRATION FORM"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="text" type="java.lang.String" value="Full Name"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="full_name">
      <AccessibilityProperties>
        <Property name="AccessibleContext.accessibleName" type="java.lang.String" value=""/>
      </AccessibilityProperties>
    </Component>
    <Component class="javax.swing.JTextField" name="username">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="text" type="java.lang.String" value="Username"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="text" type="java.lang.String" value="Phone No."/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="phone_no">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="text" type="java.lang.String" value="Address"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="address">
    </Component>
    <Component class="javax.swing.JTextField" name="email">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel6">
      <Properties>
        <Property name="text" type="java.lang.String" value="Email"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="password">
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel7">
      <Properties>
        <Property name="text" type="java.lang.String" value="Password"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JCheckBox" name="jCheckBox2">
      <Properties>
        <Property name="text" type="java.lang.String" value="Show Password"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
