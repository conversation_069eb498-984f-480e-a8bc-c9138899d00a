package dashboard_page.panels;

import javax.swing.*;
import java.awt.*;

public class SimpleUsersContentPanel extends JPanel {
    
    public SimpleUsersContentPanel() {
        initComponents();
    }
    
    private void initComponents() {
        setLayout(new BorderLayout());
        setBackground(Color.WHITE);
        
        JLabel titleLabel = new JLabel("User Management");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setHorizontalAlignment(JLabel.CENTER);
        
        JLabel messageLabel = new JLabel("This is a simplified User Management panel for testing");
        messageLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        messageLabel.setHorizontalAlignment(JLabel.CENTER);
        
        JPanel centerPanel = new JPanel(new GridLayout(2, 1));
        centerPanel.setBackground(Color.WHITE);
        centerPanel.add(titleLabel);
        centerPanel.add(messageLabel);
        
        add(centerPanel, BorderLayout.CENTER);
    }
    
    /**
     * Dummy method to match the interface of UsersContentPanel
     */
    public void loadUsersData() {
        System.out.println("SimpleUsersContentPanel.loadUsersData() called");
    }
}
