package library.management.system.login_register;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;
import javax.swing.JOptionPane;
import library.management.system.database.DatabaseConnection;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.io.File;
public class Register extends javax.swing.JFrame {

    public Register() {
        // Remove window decoration (title bar, minimize, maximize, close buttons)
        // Must be called before initComponents() makes the frame displayable
        setUndecorated(true);

        initComponents();
        password.setEchoChar('•');
        setLocationRelativeTo(null);
    }
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        jPanel3 = new javax.swing.JPanel();
        back = new javax.swing.JButton();
        register = new javax.swing.JButton();
        jPanel2 = new javax.swing.JPanel();
        jLabel2 = new javax.swing.JLabel();
        jLabel1 = new javax.swing.JLabel();
        full_name = new javax.swing.JTextField();
        username = new javax.swing.JTextField();
        jLabel3 = new javax.swing.JLabel();
        jLabel4 = new javax.swing.JLabel();
        phone_no = new javax.swing.JTextField();
        jLabel5 = new javax.swing.JLabel();
        address = new javax.swing.JTextField();
        email = new javax.swing.JTextField();
        jLabel6 = new javax.swing.JLabel();
        password = new javax.swing.JPasswordField();
        jLabel7 = new javax.swing.JLabel();
        jCheckBox2 = new javax.swing.JCheckBox();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        jPanel3.setBackground(new java.awt.Color(153, 153, 153));

        back.setText("BACK");
        back.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                backActionPerformed(evt);
            }
        });

        register.setText("Register");
        register.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                registerActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout jPanel3Layout = new javax.swing.GroupLayout(jPanel3);
        jPanel3.setLayout(jPanel3Layout);
        jPanel3Layout.setHorizontalGroup(
            jPanel3Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(jPanel3Layout.createSequentialGroup()
                .addGap(18, 18, 18)
                .addComponent(back, javax.swing.GroupLayout.PREFERRED_SIZE, 170, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addComponent(register, javax.swing.GroupLayout.PREFERRED_SIZE, 170, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(16, 16, 16))
        );
        jPanel3Layout.setVerticalGroup(
            jPanel3Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel3Layout.createSequentialGroup()
                .addContainerGap(30, Short.MAX_VALUE)
                .addGroup(jPanel3Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(register)
                    .addComponent(back))
                .addGap(30, 30, 30))
        );

        jPanel2.setBackground(new java.awt.Color(153, 153, 153));

        jLabel2.setFont(new java.awt.Font("Segoe UI", 1, 24)); // NOI18N
        jLabel2.setText("REGISTRATION FORM");

        javax.swing.GroupLayout jPanel2Layout = new javax.swing.GroupLayout(jPanel2);
        jPanel2.setLayout(jPanel2Layout);
        jPanel2Layout.setHorizontalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel2Layout.createSequentialGroup()
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addComponent(jLabel2)
                .addGap(74, 74, 74))
        );
        jPanel2Layout.setVerticalGroup(
            jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, jPanel2Layout.createSequentialGroup()
                .addContainerGap(30, Short.MAX_VALUE)
                .addComponent(jLabel2)
                .addGap(30, 30, 30))
        );

        jLabel1.setText("Full Name");

        jLabel3.setText("Username");

        jLabel4.setText("Phone No.");

        jLabel5.setText("Address");

        jLabel6.setText("Email");

        jLabel7.setText("Password");

        jCheckBox2.setText("Show Password");
        jCheckBox2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jCheckBox2ActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addComponent(jPanel3, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
            .addComponent(jPanel2, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                    .addGroup(layout.createSequentialGroup()
                        .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                        .addComponent(jCheckBox2))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(25, 25, 25)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                .addComponent(jLabel1)
                                .addGap(18, 18, 18)
                                .addComponent(full_name))
                            .addGroup(layout.createSequentialGroup()
                                .addComponent(jLabel3)
                                .addGap(18, 18, 18)
                                .addComponent(username, javax.swing.GroupLayout.DEFAULT_SIZE, 280, Short.MAX_VALUE))
                            .addGroup(layout.createSequentialGroup()
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(jLabel7)
                                    .addComponent(jLabel6))
                                .addGap(23, 23, 23)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(email)
                                    .addComponent(password)))
                            .addGroup(layout.createSequentialGroup()
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(jLabel4)
                                    .addComponent(jLabel5))
                                .addGap(18, 18, 18)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(address)
                                    .addComponent(phone_no))))))
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addComponent(jPanel2, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(30, 30, 30)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel1)
                    .addComponent(full_name, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(30, 30, 30)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel3)
                    .addComponent(username, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(30, 30, 30)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel4)
                    .addComponent(phone_no, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(30, 30, 30)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel5)
                    .addComponent(address, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(30, 30, 30)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel6)
                    .addComponent(email, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(30, 30, 30)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(jLabel7)
                    .addComponent(password, javax.swing.GroupLayout.PREFERRED_SIZE, 40, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addComponent(jCheckBox2)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, 30, Short.MAX_VALUE)
                .addComponent(jPanel3, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
        );

        full_name.getAccessibleContext().setAccessibleName("");

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void registerActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_registerActionPerformed
        // Get user input from form fields
        String fullName = full_name.getText().trim();
        String userName = username.getText().trim();
        String phoneNumber = phone_no.getText().trim();
        String userAddress = address.getText().trim();
        String userEmail = email.getText().trim();
        String userPassword = new String(password.getPassword());

        // Validate input fields
        if (fullName.isEmpty() || userName.isEmpty() || phoneNumber.isEmpty() ||
            userAddress.isEmpty() || userEmail.isEmpty() || userPassword.isEmpty()) {
            JOptionPane.showMessageDialog(this, "Please fill in all fields", "Registration Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Validate email format
        if (!userEmail.matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$")) {
            JOptionPane.showMessageDialog(this, "Please enter a valid email address", "Registration Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Validate phone number format (assuming 10-11 digits)
        if (!phoneNumber.matches("\\d{10,11}")) {
            JOptionPane.showMessageDialog(this, "Please enter a valid phone number (10-11 digits)", "Registration Error", JOptionPane.ERROR_MESSAGE);
            return;
        }

        try {
            // Get database connection
            Connection conn = DatabaseConnection.getConnection();

            // Check if username already exists
            PreparedStatement checkUsername = conn.prepareStatement("SELECT * FROM Users WHERE Username = ?");
            checkUsername.setString(1, userName);
            ResultSet usernameResult = checkUsername.executeQuery();

            if (usernameResult.next()) {
                JOptionPane.showMessageDialog(this, "Username already exists. Please choose another username.", "Registration Error", JOptionPane.ERROR_MESSAGE);
                usernameResult.close();
                checkUsername.close();
                DatabaseConnection.closeConnection(conn);
                return;
            }

            // Check if email already exists
            PreparedStatement checkEmail = conn.prepareStatement("SELECT * FROM Users WHERE Email = ?");
            checkEmail.setString(1, userEmail);
            ResultSet emailResult = checkEmail.executeQuery();

            if (emailResult.next()) {
                JOptionPane.showMessageDialog(this, "Email already registered. Please use another email address.", "Registration Error", JOptionPane.ERROR_MESSAGE);
                emailResult.close();
                checkEmail.close();
                DatabaseConnection.closeConnection(conn);
                return;
            }

            // Hash the password for security
            String hashedPassword = hashPassword(userPassword);

            // Try to insert into Users table (default schema)
            int rowsAffected = 0;
            try {
                String sql = "INSERT INTO Users (Username, PasswordHash, PhoneNumber, Address, Email, FullName, Role, RegistrationDate) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                PreparedStatement pstmt = conn.prepareStatement(sql);

                // Set parameters for the prepared statement
                pstmt.setString(1, userName);
                pstmt.setString(2, hashedPassword);
                pstmt.setString(3, phoneNumber);
                pstmt.setString(4, userAddress);
                pstmt.setString(5, userEmail);
                pstmt.setString(6, fullName);
                pstmt.setString(7, "User"); // Default role for new registrations
                pstmt.setTimestamp(8, new Timestamp(new Date().getTime())); // Current timestamp

                // Execute the SQL statement
                rowsAffected = pstmt.executeUpdate();
                pstmt.close();
                System.out.println("User inserted into default schema successfully");
            } catch (SQLException e) {
                System.out.println("Error inserting into default schema: " + e.getMessage());

                // If failed, try with APP schema
                try {
                    String sql = "INSERT INTO APP.Users (Username, PasswordHash, PhoneNumber, Address, Email, FullName, Role, RegistrationDate) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                    PreparedStatement pstmt = conn.prepareStatement(sql);

                    // Set parameters for the prepared statement
                    pstmt.setString(1, userName);
                    pstmt.setString(2, hashedPassword);
                    pstmt.setString(3, phoneNumber);
                    pstmt.setString(4, userAddress);
                    pstmt.setString(5, userEmail);
                    pstmt.setString(6, fullName);
                    pstmt.setString(7, "User"); // Default role for new registrations
                    pstmt.setTimestamp(8, new Timestamp(new Date().getTime())); // Current timestamp

                    // Execute the SQL statement
                    rowsAffected = pstmt.executeUpdate();
                    pstmt.close();
                    System.out.println("User inserted into APP schema successfully");
                } catch (SQLException ex) {
                    System.out.println("Error inserting into APP schema: " + ex.getMessage());
                    throw e; // Rethrow the original exception
                }
            }

            // Close connection
            DatabaseConnection.closeConnection(conn);

            if (rowsAffected > 0) {
                JOptionPane.showMessageDialog(this, "Registration successful! You can now login.", "Registration Success", JOptionPane.INFORMATION_MESSAGE);

                // Clear the form fields
                full_name.setText("");
                username.setText("");
                phone_no.setText("");
                address.setText("");
                email.setText("");
                password.setText("");

                // Navigate to login page
                Login login = new Login();
                login.setVisible(true);
                this.dispose(); // Close the registration form
            } else {
                JOptionPane.showMessageDialog(this, "Registration failed. Please try again.", "Registration Error", JOptionPane.ERROR_MESSAGE);
            }

        } catch (SQLException e) {
            System.err.println("SQL Error: " + e.getMessage() + " (SQLState: " + e.getSQLState() + ", Error Code: " + e.getErrorCode() + ")");
            e.printStackTrace();

            // Check for database connection issues
            if (e.getSQLState() != null &&
                (e.getSQLState().startsWith("XJ040") ||
                 e.getSQLState().equals("XJ041") ||
                 e.getSQLState().equals("40000") ||
                 e.getSQLState().equals("XBM0A") ||
                 e.getSQLState().equals("08001"))) {

                // Print detailed error information to console for debugging
                System.err.println("Database connection issue detected. Details:");
                System.err.println("SQLState: " + e.getSQLState());
                System.err.println("Error Code: " + e.getErrorCode());
                System.err.println("Message: " + e.getMessage());

                // Check for nested exceptions
                Throwable cause = e.getCause();
                while (cause != null) {
                    System.err.println("Caused by: " + cause.getMessage());
                    cause = cause.getCause();
                }

                // Offer to restart the application
                offerDatabaseRepair();
            } else {
                JOptionPane.showMessageDialog(this, "Database error: " + e.getMessage(), "Registration Error", JOptionPane.ERROR_MESSAGE);
            }
        } catch (ClassNotFoundException e) {
            JOptionPane.showMessageDialog(this, "Database driver not found: " + e.getMessage(), "Registration Error", JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "An error occurred: " + e.getMessage(), "Registration Error", JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }//GEN-LAST:event_registerActionPerformed

    private String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return password;
        }
    }

    private void backActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_backActionPerformed
        Login login = new Login();
        login.setVisible(true);
        this.dispose();
    }//GEN-LAST:event_backActionPerformed

    private void jCheckBox2ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:jCheckBox2ActionPerformed
        if (jCheckBox2.isSelected()) {
            password.setEchoChar((char) 0);
        } else {
            password.setEchoChar('•');
        }
    }//GEN-LAST:jCheckBox2ActionPerformed

    private void offerDatabaseRepair() {
        System.out.println("Database Error: The database appears to be experiencing connection issues.");

        // Show a dialog to the user
        int choice = JOptionPane.showConfirmDialog(
            this,
            "The database connection is experiencing issues.\n\n" +
            "Would you like to restart the application to attempt to fix this issue?\n\n" +
            "Note: Any unsaved data will be lost.",
            "Database Connection Issue",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE
        );

        if (choice == JOptionPane.YES_OPTION) {
            try {
                // Properly shut down the database
                System.out.println("Shutting down database connections...");
                DatabaseConnection.shutdownDatabase();

                // Wait a moment to ensure connections are closed
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    // Ignore
                }

                System.out.println("Restarting application...");
                restartApplication();
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(
                    this,
                    "Failed to restart automatically. Please close the application and restart it manually.",
                    "Restart Failed",
                    JOptionPane.ERROR_MESSAGE
                );
            }
        } else {
            // User chose not to restart
            JOptionPane.showMessageDialog(
                this,
                "You may continue to use the application, but some features might not work correctly.\n" +
                "It's recommended to close and restart the application when convenient.",
                "Database Issue",
                JOptionPane.INFORMATION_MESSAGE
            );
        }
    }

    private void restartApplication() {
        try {
            // Get the command used to start this application
            String javaBin = System.getProperty("java.home") + File.separator + "bin" + File.separator + "java";
            String classpath = System.getProperty("java.class.path");
            String className = Login.class.getName();

            // Build the command to restart
            ProcessBuilder builder = new ProcessBuilder(javaBin, "-cp", classpath, className);
            builder.start();

            // Exit this instance
            System.exit(0);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                "Failed to restart application automatically. Please restart manually.",
                "Restart Failed", JOptionPane.WARNING_MESSAGE);
            System.exit(0);
        }
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(Register.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(Register.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(Register.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(Register.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new Register().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JTextField address;
    private javax.swing.JButton back;
    private javax.swing.JTextField email;
    private javax.swing.JTextField full_name;
    private javax.swing.JCheckBox jCheckBox2;
    private javax.swing.JLabel jLabel1;
    private javax.swing.JLabel jLabel2;
    private javax.swing.JLabel jLabel3;
    private javax.swing.JLabel jLabel4;
    private javax.swing.JLabel jLabel5;
    private javax.swing.JLabel jLabel6;
    private javax.swing.JLabel jLabel7;
    private javax.swing.JPanel jPanel2;
    private javax.swing.JPanel jPanel3;
    private javax.swing.JPasswordField password;
    private javax.swing.JTextField phone_no;
    private javax.swing.JButton register;
    private javax.swing.JTextField username;
    // End of variables declaration//GEN-END:variables
}
