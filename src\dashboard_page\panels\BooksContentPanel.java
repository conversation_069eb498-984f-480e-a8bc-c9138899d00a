package dashboard_page.panels;

import forms.AddBookPanel;  // Change from import forms.AddBookFRM
import forms.EditBookPanel; // Import the new panel
import javax.swing.*;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.JTableHeader;

import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import library.management.system.database.DatabaseConnection;


public class BooksContentPanel extends JPanel {

    private JTable booksTable;
    private JTextField searchField;
    private JButton searchButton;
    private JButton addButton;
    private JButton editButton;
    private JButton deleteButton;
    private JButton refreshButton;
    private DefaultTableModel tableModel;


    public BooksContentPanel() {
        initComponents();
        loadBooksData();
    }


    public void loadBooksData() {
        try {
            tableModel.setRowCount(0);
            Connection conn = DatabaseConnection.getConnection();

            // Update query to include new columns
            String sql = "SELECT BookID, ISBN, Title, Author, Publisher, PublicationYear, " +
                         "Copyright, Genre, Edition, Copies, Status, AcquisitionDate, Condition " +
                         "FROM Books ORDER BY BookID";
            PreparedStatement pstmt = conn.prepareStatement(sql);
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                int bookId = rs.getInt("BookID");  // Still need to get the ID for edit/delete operations
                String isbn = rs.getString("ISBN");
                String title = rs.getString("Title");
                String author = rs.getString("Author");
                String publisher = rs.getString("Publisher");
                
                String publicationYear = "";
                if (rs.getObject("PublicationYear") != null) {
                    publicationYear = String.valueOf(rs.getInt("PublicationYear"));
                }
                
                String copyright = "";
                if (rs.getObject("Copyright") != null) {
                    copyright = rs.getString("Copyright");
                }
                if ((copyright == null || copyright.isEmpty()) && !publicationYear.isEmpty()) {
                    copyright = publicationYear;
                }
                
                // Get copies value
                String copies = "0";
                if (rs.getObject("Copies") != null) {
                    copies = String.valueOf(rs.getInt("Copies"));
                }
                
                // Get new column values
                String genre = rs.getString("Genre");
                genre = (genre != null) ? genre : "";
                
                String edition = rs.getString("Edition");
                edition = (edition != null) ? edition : "";
                
                String status = rs.getString("Status");
                status = (status != null) ? status : "Available";
                
                // Format acquisition date
                String acquisitionDate = "";
                java.sql.Date dateObj = rs.getDate("AcquisitionDate");
                if (dateObj != null) {
                    acquisitionDate = dateObj.toString();
                }
                
                String condition = rs.getString("Condition");
                condition = (condition != null) ? condition : "";

                tableModel.addRow(new Object[]{
                    // BookID removed from display
                    isbn,
                    title,
                    author,
                    publisher,
                    publicationYear,
                    copyright,
                    copies,
                    genre,
                    edition,
                    status,
                    acquisitionDate,
                    condition
                });
            }


            rs.close();
            pstmt.close();


            if (tableModel.getRowCount() == 0) {
                System.out.println("No books found in the database.");
                // No need to show a message dialog here
            }

        } catch (Exception e) {
            System.err.println("Error loading books: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error loading books: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    private void initComponents() {
        setLayout(new BorderLayout(10, 10));
        setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));


        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));

        searchField = new JTextField(20);

        searchField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyPressed(java.awt.event.KeyEvent e) {
                if (e.getKeyCode() == java.awt.event.KeyEvent.VK_ENTER) {
                    searchBooks();
                }
            }
        });

        searchButton = new JButton("Search");
        searchButton.setFont(new Font("Arial", Font.BOLD, 12));
        searchButton.setBackground(new Color(70, 130, 180));
        searchButton.setForeground(Color.WHITE);
        searchButton.addActionListener(e -> searchBooks());


        JButton clearButton = new JButton("Clear");
        clearButton.setFont(new Font("Arial", Font.BOLD, 12));
        clearButton.setBackground(new Color(180, 180, 180));
        clearButton.addActionListener(e -> {
            searchField.setText("");
            loadBooksData();
        });

        searchPanel.add(new JLabel("Search:"));
        searchPanel.add(searchField);
        searchPanel.add(searchButton);
        searchPanel.add(clearButton);

        add(searchPanel, BorderLayout.NORTH);


        // Update columns to remove Book ID and add the requested columns
        String[] columns = {
            "ISBN", "Title", "Author", "Publisher", "Publication Year", "Copyright", 
            "Available Copies", "Genre", "Edition", "Status", "Acquisition Date", "Condition"
        };
        
        tableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };


        booksTable = new JTable(tableModel);
        booksTable.setFillsViewportHeight(true);
        booksTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);


        booksTable.setRowHeight(35);


        booksTable.setShowGrid(true);
        booksTable.setGridColor(new Color(200, 200, 200));
        booksTable.setFont(new Font("Arial", Font.PLAIN, 12));


        JTableHeader header = booksTable.getTableHeader();
        header.setFont(new Font("Arial", Font.BOLD, 14));
        header.setBackground(new Color(220, 220, 220));
        header.setForeground(new Color(50, 50, 50));
        header.setPreferredSize(new Dimension(header.getWidth(), 40));


        ((DefaultTableCellRenderer)header.getDefaultRenderer()).setHorizontalAlignment(JLabel.CENTER);


        // Update column widths for the new configuration
        booksTable.getColumnModel().getColumn(0).setPreferredWidth(120);  // ISBN
        booksTable.getColumnModel().getColumn(1).setPreferredWidth(180);  // Title
        booksTable.getColumnModel().getColumn(2).setPreferredWidth(150);  // Author
        booksTable.getColumnModel().getColumn(3).setPreferredWidth(130);  // Publisher
        booksTable.getColumnModel().getColumn(4).setPreferredWidth(120);  // Publication Year
        booksTable.getColumnModel().getColumn(5).setPreferredWidth(120);  // Copyright
        booksTable.getColumnModel().getColumn(6).setPreferredWidth(120);  // Available Copies
        booksTable.getColumnModel().getColumn(7).setPreferredWidth(100);  // Genre
        booksTable.getColumnModel().getColumn(8).setPreferredWidth(80);   // Edition
        booksTable.getColumnModel().getColumn(9).setPreferredWidth(100);  // Status
        booksTable.getColumnModel().getColumn(10).setPreferredWidth(120); // Acquisition Date
        booksTable.getColumnModel().getColumn(11).setPreferredWidth(100); // Condition


        booksTable.setDefaultRenderer(Object.class, new DefaultTableCellRenderer() {
            @Override
            public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus, int row, int column) {
                Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

                if (!isSelected) {
                    c.setBackground(row % 2 == 0 ? new Color(245, 245, 245) : Color.WHITE);
                    c.setForeground(new Color(50, 50, 50));
                } else {
                    c.setBackground(new Color(230, 230, 250));
                    c.setForeground(new Color(50, 50, 50));
                }


                if (c instanceof JLabel) {
                    JLabel label = (JLabel) c;
                    label.setBorder(BorderFactory.createEmptyBorder(0, 8, 0, 8));

                    if (column == 0 || column == 4) {
                        label.setHorizontalAlignment(JLabel.CENTER);
                    } else {
                        label.setHorizontalAlignment(JLabel.LEFT);
                    }
                }

                return c;
            }
        });


        booksTable.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                int selectedRow = booksTable.getSelectedRow();
                if (selectedRow >= 0) {

                    editButton.setEnabled(true);
                    deleteButton.setEnabled(true);
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(booksTable);
        add(scrollPane, BorderLayout.CENTER);


        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        buttonPanel.setBackground(new Color(240, 240, 240));

        addButton = new JButton("Add Book");
        addButton.setFont(new Font("Arial", Font.BOLD, 14));
        addButton.setBackground(new Color(0, 153, 51));
        addButton.setForeground(Color.WHITE);
        addButton.setFocusPainted(false);
        addButton.setPreferredSize(new Dimension(120, 40));
        addButton.addActionListener(e -> openAddBookForm());

        editButton = new JButton("Edit Book");
        editButton.setFont(new Font("Arial", Font.BOLD, 14));
        editButton.setBackground(new Color(0, 102, 204));
        editButton.setForeground(Color.WHITE);
        editButton.setFocusPainted(false);
        editButton.setPreferredSize(new Dimension(120, 40));
        editButton.addActionListener(e -> openEditBookForm());
        editButton.setEnabled(false);

        deleteButton = new JButton("Delete Book");
        deleteButton.setFont(new Font("Arial", Font.BOLD, 14));
        deleteButton.setBackground(new Color(204, 0, 0));
        deleteButton.setForeground(Color.WHITE);
        deleteButton.setFocusPainted(false);
        deleteButton.setPreferredSize(new Dimension(120, 40));
        deleteButton.addActionListener(e -> deleteSelectedBook());
        deleteButton.setEnabled(false);


        refreshButton = new JButton("Refresh");
        refreshButton.setFont(new Font("Arial", Font.BOLD, 14));
        refreshButton.setBackground(new Color(100, 181, 246));
        refreshButton.setForeground(Color.WHITE);
        refreshButton.setFocusPainted(false);
        refreshButton.setPreferredSize(new Dimension(120, 40));
        refreshButton.addActionListener(e -> loadBooksData());

        buttonPanel.add(refreshButton);
        buttonPanel.add(addButton);
        buttonPanel.add(editButton);
        buttonPanel.add(deleteButton);

        add(buttonPanel, BorderLayout.SOUTH);
    }


    private void openAddBookForm() {
        try {
            System.out.println("Opening Add Book panel...");
            
            // Create a dialog to host our AddBookPanel
            JDialog addBookDialog = new JDialog((Frame)SwingUtilities.getWindowAncestor(this), "Add New Book", true);
            addBookDialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
            
            // Create our AddBookPanel with a listener to refresh the book list when a book is added
            AddBookPanel addBookPanel = new AddBookPanel(new AddBookPanel.BookAddedListener() {
                @Override
                public void onBookAdded() {
                    // Reload the books data when a book is successfully added
                    loadBooksData();
                }
            });
            
            // Add the panel to the dialog
            addBookDialog.getContentPane().add(addBookPanel);
            addBookDialog.pack();
            addBookDialog.setSize(500, 650); // Adjust size for simpler UI
            addBookDialog.setLocationRelativeTo(null);
            
            // Show the dialog
            addBookDialog.setVisible(true);
            
            System.out.println("Add Book panel opened successfully");
        } catch (Exception e) {
            System.err.println("Error opening Add Book panel: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error opening Add Book panel: " + e.getMessage(),
                "Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }


    private void openEditBookForm() {
        int selectedRow = booksTable.getSelectedRow();
        if (selectedRow >= 0) {
            // Since BookID is no longer in the table, get it from the database using ISBN
            try {
                String isbn = booksTable.getValueAt(selectedRow, 0).toString(); // ISBN is now in column 0
                
                Connection conn = DatabaseConnection.getConnection();
                String sql = "SELECT BookID FROM Books WHERE ISBN = ?";
                PreparedStatement pstmt = conn.prepareStatement(sql);
                pstmt.setString(1, isbn);
                ResultSet rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    int bookIdInt = rs.getInt("BookID");
                    
                    // Continue with the existing code to open the edit panel
                    JDialog editBookDialog = new JDialog((Frame)SwingUtilities.getWindowAncestor(this), "Edit Book", true);
                    editBookDialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
                    
                    EditBookPanel editBookPanel = new EditBookPanel(bookIdInt, new EditBookPanel.BookEditedListener() {
                        @Override
                        public void onBookEdited() {
                            loadBooksData();
                        }
                    });
                    
                    editBookDialog.getContentPane().add(editBookPanel);
                    editBookDialog.pack();
                    editBookDialog.setSize(500, 650);
                    editBookDialog.setLocationRelativeTo(null);
                    editBookDialog.setVisible(true);
                } else {
                    JOptionPane.showMessageDialog(this, 
                        "Could not find book with ISBN: " + isbn,
                        "Error", 
                        JOptionPane.ERROR_MESSAGE);
                }
                
                rs.close();
                pstmt.close();
                DatabaseConnection.closeConnection(conn);
            } catch (Exception e) {
                System.err.println("Error opening Edit Book panel: " + e.getMessage());
                e.printStackTrace();
                JOptionPane.showMessageDialog(this,
                    "Error opening Edit Book panel: " + e.getMessage(),
                    "Error",
                    JOptionPane.ERROR_MESSAGE);
            }
        }
    }


    private void deleteSelectedBook() {
        int selectedRow = booksTable.getSelectedRow();
        if (selectedRow >= 0) {
            String isbn = booksTable.getValueAt(selectedRow, 0).toString(); // ISBN is now in column 0
            String title = booksTable.getValueAt(selectedRow, 1).toString(); // Title is now in column 1
            
            int confirm = JOptionPane.showConfirmDialog(
                this,
                "Are you sure you want to delete the book: " + title + "?\n\n" +
                "WARNING: This will also delete all copies of this book and related loan records.\n" +
                "This action cannot be undone!",
                "Confirm Force Delete",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE
            );
            
            if (confirm == JOptionPane.YES_OPTION) {
                Connection conn = null;
                boolean autoCommit = true;
                
                try {
                    conn = DatabaseConnection.getConnection();
                    
                    // First, get the BookID
                    String getBookIdQuery = "SELECT BookID FROM Books WHERE ISBN = ?";
                    PreparedStatement getBookIdStmt = conn.prepareStatement(getBookIdQuery);
                    getBookIdStmt.setString(1, isbn);
                    ResultSet rs = getBookIdStmt.executeQuery();
                    
                    if (rs.next()) {
                        int bookId = rs.getInt("BookID");
                        
                        // Save current auto-commit state
                        autoCommit = conn.getAutoCommit();
                        
                        // Start transaction
                        conn.setAutoCommit(false);
                        
                        // Continue with the rest of the deletion process using bookId
                        // 1. Delete all loans related to this book's copies one by one
                        String getCopyIdsQuery = "SELECT CopyID FROM BookCopies WHERE BookID = ?";
                        PreparedStatement getCopyIdsStmt = conn.prepareStatement(getCopyIdsQuery);
                        getCopyIdsStmt.setInt(1, bookId);
                        ResultSet copyIdsRs = getCopyIdsStmt.executeQuery();
                        
                        while (copyIdsRs.next()) {
                            int copyId = copyIdsRs.getInt("CopyID");
                            String deleteCopyLoansQuery = "DELETE FROM Loans WHERE CopyID = ?";
                            PreparedStatement deleteCopyLoansStmt = conn.prepareStatement(deleteCopyLoansQuery);
                            deleteCopyLoansStmt.setInt(1, copyId);
                            deleteCopyLoansStmt.executeUpdate();
                            deleteCopyLoansStmt.close();
                        }
                        
                        copyIdsRs.close();
                        getCopyIdsStmt.close();
                        
                        // 2. Now delete book copies
                        String deleteCopiesQuery = "DELETE FROM BookCopies WHERE BookID = ?";
                        PreparedStatement deleteCopiesStmt = conn.prepareStatement(deleteCopiesQuery);
                        deleteCopiesStmt.setInt(1, bookId);
                        deleteCopiesStmt.executeUpdate();
                        deleteCopiesStmt.close();
                        
                        // 3. Finally delete the book
                        String deleteBookQuery = "DELETE FROM Books WHERE BookID = ?";
                        PreparedStatement deleteBookStmt = conn.prepareStatement(deleteBookQuery);
                        deleteBookStmt.setInt(1, bookId);
                        int rowsAffected = deleteBookStmt.executeUpdate();
                        deleteBookStmt.close();
                        
                        if (rowsAffected > 0) {
                            // Commit the transaction
                            conn.commit();
                            
                            // Update the table
                            tableModel.removeRow(selectedRow);
                            
                            editButton.setEnabled(false);
                            deleteButton.setEnabled(false);
                            
                            JOptionPane.showMessageDialog(
                                this, 
                                "Book and all related records deleted successfully", 
                                "Success", 
                                JOptionPane.INFORMATION_MESSAGE
                            );
                        } else {
                            conn.rollback();
                            JOptionPane.showMessageDialog(
                                this, 
                                "Failed to delete book from database", 
                                "Error", 
                                JOptionPane.ERROR_MESSAGE
                            );
                        }

                    } else {
                        JOptionPane.showMessageDialog(
                            this, 
                            "Could not find book with ISBN: " + isbn, 
                            "Error", 
                            JOptionPane.ERROR_MESSAGE
                        );
                        return;
                    }
                    
                    rs.close();
                    getBookIdStmt.close();
                    
                } catch (Exception e) {
                    System.err.println("Error deleting book: " + e.getMessage());
                    e.printStackTrace();
                    
                    try {
                        // Roll back the transaction on error
                        if (conn != null) {
                            conn.rollback();
                        }
                    } catch (SQLException rollbackEx) {
                        System.err.println("Error rolling back transaction: " + rollbackEx.getMessage());
                    }
                    
                    JOptionPane.showMessageDialog(
                        this,
                        "Error deleting book: " + e.getMessage(),
                        "Database Error",
                        JOptionPane.ERROR_MESSAGE
                    );
                } finally {
                    try {
                        // Restore original auto-commit state
                        if (conn != null) {
                            conn.setAutoCommit(autoCommit);
                            DatabaseConnection.closeConnection(conn);
                        }
                    } catch (SQLException finalEx) {
                        System.err.println("Error closing connection: " + finalEx.getMessage());
                    }
                }
            }
        }
    }


    private void searchBooks() {
        String searchTerm = searchField.getText().trim();

        if (searchTerm.isEmpty()) {
            loadBooksData();
            return;
        }

        try {
            tableModel.setRowCount(0);
            Connection conn = DatabaseConnection.getConnection();

            // Update query to include new columns
            String sql = "SELECT BookID, ISBN, Title, Author, Publisher, PublicationYear, " +
                         "Copyright, Genre, Edition, Copies, Status, AcquisitionDate, Condition " +
                         "FROM Books " +
                         "WHERE LOWER(Title) LIKE ? OR LOWER(Author) LIKE ? OR LOWER(ISBN) LIKE ? " +
                         "ORDER BY BookID";

            PreparedStatement pstmt = conn.prepareStatement(sql);

            String likeParam = "%" + searchTerm.toLowerCase() + "%";
            pstmt.setString(1, likeParam);
            pstmt.setString(2, likeParam);
            pstmt.setString(3, likeParam);

            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                int bookId = rs.getInt("BookID");  // Still need for operations
                String isbn = rs.getString("ISBN");
                String title = rs.getString("Title");
                String author = rs.getString("Author");
                String publisher = rs.getString("Publisher");

                String publicationYear = "";
                if (rs.getObject("PublicationYear") != null) {
                    publicationYear = String.valueOf(rs.getInt("PublicationYear"));
                }
                
                // Get Copyright field
                String copyright = "";
                if (rs.getObject("Copyright") != null) {
                    copyright = rs.getString("Copyright");
                }
                // If copyright is empty but publication year exists, use that
                if ((copyright == null || copyright.isEmpty()) && !publicationYear.isEmpty()) {
                    copyright = publicationYear;
                }

                // Get copies value
                String copies = "0";
                if (rs.getObject("Copies") != null) {
                    copies = String.valueOf(rs.getInt("Copies"));
                }
                
                // Get new column values
                String genre = rs.getString("Genre");
                genre = (genre != null) ? genre : "";
                
                String edition = rs.getString("Edition");
                edition = (edition != null) ? edition : "";
                
                String status = rs.getString("Status");
                status = (status != null) ? status : "Available";
                
                // Format acquisition date
                String acquisitionDate = "";
                java.sql.Date dateObj = rs.getDate("AcquisitionDate");
                if (dateObj != null) {
                    acquisitionDate = dateObj.toString();
                }
                
                String condition = rs.getString("Condition");
                condition = (condition != null) ? condition : "";

                tableModel.addRow(new Object[]{
                    // BookID removed from display
                    isbn,
                    title,
                    author,
                    publisher,
                    publicationYear,
                    copyright,
                    copies,
                    genre,
                    edition,
                    status,
                    acquisitionDate,
                    condition
                });
            }

            rs.close();
            pstmt.close();

            // No need to show a message dialog when no books are found

        } catch (Exception e) {
            System.err.println("Error searching books: " + e.getMessage());
            e.printStackTrace();
            JOptionPane.showMessageDialog(this,
                "Error searching books: " + e.getMessage(),
                "Database Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

}
